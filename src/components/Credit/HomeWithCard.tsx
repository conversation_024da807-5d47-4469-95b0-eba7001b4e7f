import React, { useState, useEffect } from "react";
import {
  FaChartPie,
  FaExclamationTriangle,
  FaExchangeAlt,
  FaInfoCircle,
  FaMoneyBillWave,
  FaFilter,
  FaPlus,
  FaTimes,
  FaDownload,
  FaCalendarDay,
  FaArrowRight,
  FaRegBell,
  FaPlane,
  FaShoppingCart,
  FaShieldAlt,
  FaUniversity,
  FaGasPump,
  FaCreditCard,
} from "react-icons/fa";
import QuilttContainer from "./QuilttContainer";
import {
  creditCardApi,
  aiRecommendationsApi,
  spendingInsightsApi,
  affiliateOffersApi,
  AffiliateOffer,
} from "../../services/api";
import { quilttApi } from "../../services/quilttApi";
import { BankIcon } from "../../utils/bankIcons";

const HomeWithCard: React.FC = () => {
  const [month, setMonth] = useState<string>("May 2023");
  const [currentSlide, setCurrentSlide] = useState<number>(0);
  const [showQuiltt, setShowQuiltt] = useState<boolean>(false);
  const [showBalanceTransferModal, setShowBalanceTransferModal] = useState<boolean>(false);
  const [cards, setCards] = useState<any[]>([]);
  const [balanceTransferCards, setBalanceTransferCards] = useState<any[]>([]);
  const [isLoadingBalanceTransfer, setIsLoadingBalanceTransfer] = useState<boolean>(false);
  const [statsData, setStatsData] = useState({
    totalLimit: 0,
    totalBalance: 0,
    available: 0,
    utilizedPercentage: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] =
    useState<boolean>(true);
  const [spendingInsights, setSpendingInsights] = useState<any[]>([]);
  const [isLoadingInsights, setIsLoadingInsights] = useState<boolean>(true);
  const [affiliateOffers, setAffiliateOffers] = useState<AffiliateOffer[]>([]);
  const [isLoadingAffiliateOffers, setIsLoadingAffiliateOffers] = useState<boolean>(true);
  const [showAlertCarousel, setShowAlertCarousel] = useState<boolean>(true);

  // Transform Quiltt credit card data to match existing structure
  const transformQuilttCreditCard = (account: any) => {
    const currentBalance = Math.abs(account.balance.current); // Make positive for display
    const creditLimit = account.balance.available + currentBalance; // Available + current balance
    const utilizedPercentage = creditLimit > 0 ? Math.round((currentBalance / creditLimit) * 100) : 0;

    // Extract last 4 characters from account ID (could be alphanumeric)
    const lastFourFromId = account.id.slice(-4);

    return {
      id: `quiltt_${account.id}`,
      name: `${account.institution.name} ${account.name}`,
      type: "Personal", // Default to Personal, could be enhanced based on metadata
      issuer: account.institution.name.toLowerCase().replace(/\s+/g, ""),
      last_four: lastFourFromId,
      icon: account.institution.name.toLowerCase().replace(/\s+/g, ""),
      current_balance: currentBalance.toFixed(2),
      credit_limit: creditLimit.toFixed(2),
      payment_due_date: null,
      balance_transfer: "", // Default empty
      due_amount: currentBalance.toFixed(2),
      apr_end_date: null,
      status: "good",
      create_at: new Date().toISOString(),
      update_at: new Date().toISOString(),
      tags: ["Quiltt", "Connected"],
      tagColors: {
        "Quiltt": "blue",
        "Connected": "green"
      },
      utilizedPercentage,
      daysLeft: null,
      aprEndDate: null,
      isQuilttCard: true // Flag to identify Quiltt cards
    };
  };

  // Fetch cards and stats data
  useEffect(() => {
    const fetchCardsAndStats = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch cards from main API
        const cardsResponse = await creditCardApi.getCards(currentPage);
        let mainApiCards = [];

        if (cardsResponse.success) {
          mainApiCards = cardsResponse.cards;
          setTotalPages(cardsResponse.pagination.totalPages);
          setCurrentPage(cardsResponse.pagination.currentPage);
        } else {
          setError(cardsResponse.message || "Failed to load credit cards");
        }

        // Fetch credit cards from Quiltt API
        let quilttCreditCards: any[] = [];
        try {
          const auth_token = localStorage.getItem("auth_token");
          if (auth_token) {
            // Get Quiltt token and fetch accounts
            await quilttApi.getToken(auth_token);
            const quilttAccounts = await quilttApi.getAccounts();

            // Filter for credit card accounts and transform them
            quilttCreditCards = quilttAccounts
              .filter(account => account.type === "CREDIT")
              .map(transformQuilttCreditCard);
          }
        } catch (quilttError) {
          console.warn("Failed to fetch Quiltt credit cards:", quilttError);
          // Don't fail the entire operation if Quiltt fails
        }

        // Combine both sets of cards
        const allCards = [...mainApiCards, ...quilttCreditCards];
        setCards(allCards);

        // Fetch credit stats
        const statsResponse = await creditCardApi.getStats();

        if (statsResponse.success) {
          setStatsData(statsResponse.stats);
        }
      } catch (err: any) {
        setError(
          err.response?.data?.message ||
            "An error occurred while loading credit card data"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchCardsAndStats();
  }, [currentPage]);

  // Fetch AI recommendations
  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        setIsLoadingRecommendations(true);

        // Only fetch AI recommendations if user has cards
        if (cards.length > 0) {
          // Prepare card names and user context for the new AI endpoint
          const cardNames = cards
            .filter(card => !card.sponsored)
            .map(card => card.name);

          // Build user context from available card data
          const userContext = {
            spending_patterns: {
              dining: 800,    // Sample values - in a real app, these would come from actual spending data
              travel: 600,
              groceries: 400,
              gas: 200,
              entertainment: 150,
              shopping: 300
            },
            preferences: cards.flatMap(card => card.tags || []).filter(Boolean),
            current_cards: cardNames
          };

          const payload = {
            card_names: cardNames,
            user_context: userContext
          };

          try {
            const response = await aiRecommendationsApi.getPersonalizedRecommendations(payload);

            if (response.success || response.best_card_recommendations) {
              // Transform the API response to match our UI format
              const transformedRecommendations: any[] = [];

              // Add best card recommendations
              if (response.best_card_recommendations) {
                response.best_card_recommendations.forEach((rec: any, index: number) => {
                  transformedRecommendations.push({
                    id: `best_card_${index}`,
                    type: `Best Card for ${rec.category}`,
                    title: `Best Card for ${rec.category}: ${rec.card_name}`,
                    description: `${rec.reason}. ${rec.potential_rewards}`,
                    icon: rec.category.toLowerCase() === 'dining' ? 'utensils' :
                          rec.category.toLowerCase() === 'travel' ? 'plane' :
                          rec.category.toLowerCase() === 'gas' ? 'gas-pump' : 'credit-card',
                    iconBg: rec.category.toLowerCase() === 'dining' ? 'yellow' :
                            rec.category.toLowerCase() === 'travel' ? 'blue' :
                            rec.category.toLowerCase() === 'gas' ? 'orange' : 'green'
                  });
                });
              }

              // Add APR alerts
              if (response.apr_alerts) {
                response.apr_alerts.forEach((alert: any, index: number) => {
                  transformedRecommendations.push({
                    id: `apr_alert_${index}`,
                    type: "0% APR Ending Soon",
                    title: "0% APR Ending Soon",
                    description: `Your ${alert.card_name} 0% APR ends in ${alert.days_remaining} days. ${alert.recommendation}`,
                    icon: "exclamation-triangle",
                    iconBg: "red"
                  });
                });
              }

              // Add balance transfer opportunities
              if (response.balance_transfer_opportunities) {
                response.balance_transfer_opportunities.forEach((transfer: any, index: number) => {
                  transformedRecommendations.push({
                    id: `balance_transfer_${index}`,
                    type: "Balance Transfer Opportunity",
                    title: "Balance Transfer Opportunity",
                    description: `Transfer $${transfer.amount.toLocaleString()} from ${transfer.from_card} to ${transfer.to_card} to save $${transfer.potential_savings} in interest.`,
                    icon: "exchange-alt",
                    iconBg: "blue"
                  });
                });
              }

              setRecommendations(transformedRecommendations);

              // Store spending insights separately for the spending insight section
              if (response.spending_insights) {
                setSpendingInsights(response.spending_insights);
              }
            } else {
              // Fall back to default recommendations
              setRecommendations(getDefaultRecommendations());
            }
          } catch (aiError) {
            console.warn("AI recommendations API failed, using defaults:", aiError);
            setRecommendations(getDefaultRecommendations());
          }
        } else {
          // No cards, show empty recommendations
          setRecommendations([]);
        }
      } catch (err) {
        console.error("Error fetching recommendations:", err);
        setRecommendations(getDefaultRecommendations());
      } finally {
        setIsLoadingRecommendations(false);
      }
    };

    // Helper function for default recommendations
    const getDefaultRecommendations = () => [
      {
        id: "default_1",
        type: "Best Card for Dining",
        title: "Best Card for Dining: Amex Gold",
        description: "Use this card for restaurants to earn 4x points on dining purchases.",
        icon: "utensils",
        iconBg: "yellow",
      },
      {
        id: "default_2",
        type: "0% APR Ending Soon",
        title: "0% APR Ending Soon",
        description: "Your Chase Sapphire 0% APR ends in 30 days. Consider a balance transfer.",
        icon: "exclamation-triangle",
        iconBg: "red",
      },
      {
        id: "default_3",
        type: "Balance Transfer Opportunity",
        title: "Balance Transfer Opportunity",
        description: "Transfer $3,500 from Chase to Capital One to save $420 in interest.",
        icon: "exchange-alt",
        iconBg: "blue",
      },
    ];

    // Only fetch recommendations after cards are loaded
    if (!isLoading) {
      fetchRecommendations();
    }
  }, [cards, isLoading]);

  // Fetch spending insights
  useEffect(() => {
    const fetchSpendingInsights = async () => {
      try {
        setIsLoadingInsights(true);

        const response = await spendingInsightsApi.getInsights();

        if (response.success) {
          setSpendingInsights(response.insights);
        } else {
          // Empty array if no insights
          setSpendingInsights([]);
        }
      } catch (err) {
        setSpendingInsights([]);
      } finally {
        setIsLoadingInsights(false);
      }
    };

    fetchSpendingInsights();
  }, []);

  // Fetch affiliate offers
  useEffect(() => {
    const fetchAffiliateOffers = async () => {
      try {
        setIsLoadingAffiliateOffers(true);

        const response = await affiliateOffersApi.getSponsoredOffers(3);

        if (response.success) {
          setAffiliateOffers(response.offers);
        } else {
          // Use default affiliate offers if API fails
          setAffiliateOffers([
            {
              id: 1,
              name: "Identity Protection by Identity IQ",
              category: "Identity Theft Protection",
              description: "Advanced credit monitoring and identity theft protection",
              tags: ["Sponsored"],
              categories: ["Identity Theft", "Credit Monitoring"],
              backgroundColor: "bg-blue-500",
              icon: "shield",
              partnerUrl: "https://www.identityiq.com",
              featured: false,
              sponsored: true,
            },
            {
              id: 2,
              name: "Novo Business Banking",
              category: "Business Banking",
              description: "$300 welcome bonus & no monthly fees",
              tags: ["Sponsored"],
              categories: ["Banking", "5% Back"],
              backgroundColor: "bg-green-500",
              icon: "bank",
              partnerUrl: "https://www.novo.co",
              featured: false,
              sponsored: true,
            },
          ]);
        }
      } catch (err) {
        // Use default affiliate offers on error
        setAffiliateOffers([
          {
            id: 1,
            name: "Identity Protection by Identity IQ",
            category: "Identity Theft Protection",
            description: "Advanced credit monitoring and identity theft protection",
            tags: ["Sponsored"],
            categories: ["Identity Theft", "Credit Monitoring"],
            backgroundColor: "bg-blue-500",
            icon: "shield",
            partnerUrl: "https://www.identityiq.com",
            featured: false,
            sponsored: true,
          },
          {
            id: 2,
            name: "Novo Business Banking",
            category: "Business Banking",
            description: "$300 welcome bonus & no monthly fees",
            tags: ["Sponsored"],
            categories: ["Banking", "5% Back"],
            backgroundColor: "bg-green-500",
            icon: "bank",
            partnerUrl: "https://www.novo.co",
            featured: false,
            sponsored: true,
          },
        ]);
      } finally {
        setIsLoadingAffiliateOffers(false);
      }
    };

    fetchAffiliateOffers();
  }, []);

  // Alert carousel slides
  const alertSlides = [
    {
      id: 1,
      title: "Important Alert",
      content:
        "Your 0% APR promotional period for Chase Sapphire will end in 30 days. Consider transferring your balance or setting up a payment plan.",
    },
    {
      id: 2,
      title: "Payment Due",
      content:
        "Your Amex Gold payment of $350 is due in 5 days. Make sure to pay on time to avoid late fees.",
    },
    {
      id: 3,
      title: "Reward Points",
      content:
        "You have 15,000 unused reward points on your Chase Sapphire card. Consider redeeming them before they expire.",
    },
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === alertSlides.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? alertSlides.length - 1 : prev - 1));
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const handleCloseAlert = () => {
    setShowAlertCarousel(false);
  };

  // Quiltt handlers
  const handleQuilttSuccess = () => {
    // Handle successful card connection
    setShowQuiltt(false);
    // Reload cards data
    window.location.reload();
  };

  const handleQuilttExit = () => {
    setShowQuiltt(false);
  };

  const handleQuilttError = () => {
    setShowQuiltt(false);
  };

  // Balance Transfer Modal handlers
  const fetchBalanceTransferCards = async () => {
    try {
      setIsLoadingBalanceTransfer(true);

      // Fetch all cards
      const response = await creditCardApi.getCards(1, 100); // Get all cards with a high limit

      if (response.success) {
        // Filter cards that have balance_transfer value (not empty or null)
        const cardsWithBalanceTransfer = response.cards.filter((card: any) =>
          card.balance_transfer &&
          card.balance_transfer !== "" &&
          parseFloat(card.balance_transfer) > 0
        );

        setBalanceTransferCards(cardsWithBalanceTransfer);
      } else {
        console.error("Failed to fetch cards:", response.message);
        setBalanceTransferCards([]);
      }
    } catch (error) {
      console.error("Error fetching balance transfer cards:", error);
      setBalanceTransferCards([]);
    } finally {
      setIsLoadingBalanceTransfer(false);
    }
  };

  const handleFindBalanceTransferCards = () => {
    setShowBalanceTransferModal(true);
    fetchBalanceTransferCards();
  };

  const handleCloseBalanceTransferModal = () => {
    setShowBalanceTransferModal(false);
    setBalanceTransferCards([]);
  };

  // Debug function to check if click handlers are working
  const handleAddCardClick = () => {
    setShowQuiltt(true);
  };

  // Handle affiliate offer clicks
  const handleAffiliateClick = async (offer: AffiliateOffer) => {
    try {
      // Track the click
      await affiliateOffersApi.trackClick(offer.id);
      // Open the partner URL
      window.open(offer.partnerUrl, "_blank", "noopener,noreferrer");
    } catch (error) {
      // If tracking fails, still open the URL
      window.open(offer.partnerUrl, "_blank", "noopener,noreferrer");
    }
  };

  // Function to generate insertion pattern based on different strategies
  const generateInsertionPattern = (strategy: 'figma' | 'even' | 'custom' = 'figma', customPattern?: number[]) => {
    switch (strategy) {
      case 'figma':
        // Figma pattern: after 1st card, then after every 2 cards
        // Results in: 1 card, affiliate, 2 cards, affiliate, 2 cards, etc.
        return [1, 3, 5, 7, 9, 11, 13, 15];
      case 'even':
        // Even distribution: after every 2 cards
        return [2, 4, 6, 8, 10, 12, 14, 16];
      case 'custom':
        return customPattern || [1, 3, 5];
      default:
        return [1, 3, 5];
    }
  };

  // Function to intersperse affiliate offers between credit cards
  const createInterspersedCardList = (insertionStrategy: 'figma' | 'even' | 'custom' = 'figma') => {
    const regularCards = cards.filter(card => !card.sponsored);
    const sponsoredCards = cards.filter(card => card.sponsored);
    const result: Array<{ type: 'card' | 'affiliate'; data: any; key: string }> = [];

    // Generate insertion pattern based on strategy
    const insertionPattern = generateInsertionPattern(insertionStrategy);
    let affiliateIndex = 0;

    // Add all regular cards first
    regularCards.forEach((card, index) => {
      result.push({
        type: 'card',
        data: card,
        key: `card-${card.id}`
      });

      // Check if we should insert an affiliate after this card
      const currentPosition = index + 1; // 1-indexed position
      if (insertionPattern.includes(currentPosition) &&
          affiliateIndex < affiliateOffers.length) {
        result.push({
          type: 'affiliate',
          data: affiliateOffers[affiliateIndex],
          key: `affiliate-${affiliateOffers[affiliateIndex].id}`
        });
        affiliateIndex++;
      }
    });

    // Add any remaining sponsored cards at the end
    sponsoredCards.forEach(card => {
      result.push({
        type: 'card',
        data: card,
        key: `sponsored-card-${card.id}`
      });
    });

    return result;
  };

  if (isLoading && cards.length === 0) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-center items-center min-h-[50vh]">
          <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error && cards.length === 0) {
    return (
      <div className="max-w-6xl mx-auto">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  // Continue with the rest of the component

  return (
    <div className="max-w-6xl mx-auto">
      {/* Alert Carousel */}

      {/* Credit Card Overview Stats - Top Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4 flex flex-row gap-4">
          {/* Personal Cards */}
          <div className="w-1/2 bg-blue-50 rounded-lg shadow-sm p-4 flex flex-col items-center justify-center">
            <div className="bg-blue-100 p-4 rounded-full mb-2">
              <svg
                width="24"
                height="20"
                viewBox="0 0 24 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M23.4062 20H0.90625V0H23.4062V20Z" stroke="#E5E7EB" />
                <g clip-path="url(#clip0_247_11838)">
                  <path
                    d="M3.40625 1.25C2.02734 1.25 0.90625 2.37109 0.90625 3.75V5H23.4062V3.75C23.4062 2.37109 22.2852 1.25 20.9062 1.25H3.40625ZM23.4062 8.75H0.90625V16.25C0.90625 17.6289 2.02734 18.75 3.40625 18.75H20.9062C22.2852 18.75 23.4062 17.6289 23.4062 16.25V8.75ZM5.28125 13.75H7.78125C8.125 13.75 8.40625 14.0312 8.40625 14.375C8.40625 14.7188 8.125 15 7.78125 15H5.28125C4.9375 15 4.65625 14.7188 4.65625 14.375C4.65625 14.0312 4.9375 13.75 5.28125 13.75ZM9.65625 14.375C9.65625 14.0312 9.9375 13.75 10.2812 13.75H15.2812C15.625 13.75 15.9062 14.0312 15.9062 14.375C15.9062 14.7188 15.625 15 15.2812 15H10.2812C9.9375 15 9.65625 14.7188 9.65625 14.375Z"
                    fill="#2563EB"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_247_11838">
                    <path d="M0.90625 0H23.4062V20H0.90625V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
            <p className="text-3xl font-bold text-gray-800 mb-1">
              {
                cards.filter(
                  (card) => !card.sponsored && card.type === "Personal"
                ).length
              }
            </p>
            <p className="text-sm text-gray-600">Personal Cards</p>
          </div>

          {/* Business Cards */}
          <div className="w-1/2 bg-purple-50 rounded-lg shadow-sm p-4 flex flex-col items-center justify-center">
            <div className="bg-purple-100 p-4 rounded-full mb-2">
              <svg
                width="21"
                height="20"
                viewBox="0 0 21 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M20.4844 20H0.484375V0H20.4844V20Z" stroke="#E5E7EB" />
                <g clip-path="url(#clip0_247_11848)">
                  <path
                    d="M7.67188 1.875H13.2969C13.4688 1.875 13.6094 2.01562 13.6094 2.1875V3.75H7.35938V2.1875C7.35938 2.01562 7.5 1.875 7.67188 1.875ZM5.48438 2.1875V3.75H2.98438C1.60547 3.75 0.484375 4.87109 0.484375 6.25V10H7.98438H12.9844H20.4844V6.25C20.4844 4.87109 19.3633 3.75 17.9844 3.75H15.4844V2.1875C15.4844 0.980469 14.5039 0 13.2969 0H7.67188C6.46484 0 5.48438 0.980469 5.48438 2.1875ZM20.4844 11.25H12.9844V12.5C12.9844 13.1914 12.4258 13.75 11.7344 13.75H9.23438C8.54297 13.75 7.98438 13.1914 7.98438 12.5V11.25H0.484375V16.25C0.484375 17.6289 1.60547 18.75 2.98438 18.75H17.9844C19.3633 18.75 20.4844 17.6289 20.4844 16.25V11.25Z"
                    fill="#7C3AED"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_247_11848">
                    <path d="M0.484375 0H20.4844V20H0.484375V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
            <p className="text-3xl font-bold text-gray-800 mb-1">
              {
                cards.filter(
                  (card) => !card.sponsored && card.type === "Business"
                ).length
              }
            </p>
            <p className="text-sm text-gray-600">Business Cards</p>
          </div>
        </div>

        {/* Credit Usage */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          {/* justify-between */}
          <div className="flex justify-between">
            <h3 className="text-base font-medium text-gray-700 mb-3">
              Credit Usage
            </h3>
            <svg
              width="33"
              height="32"
              viewBox="0 0 33 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24.3281 0C28.7464 0 32.3281 3.58172 32.3281 8V24C32.3281 28.4183 28.7464 32 24.3281 32H8.32812C3.90985 32 0.328125 28.4183 0.328125 24V8C0.328125 3.58172 3.90985 0 8.32812 0H24.3281Z"
                fill="#D1FAE5"
              />
              <path
                d="M24.3281 0C28.7464 0 32.3281 3.58172 32.3281 8V24C32.3281 28.4183 28.7464 32 24.3281 32H8.32812C3.90985 32 0.328125 28.4183 0.328125 24V8C0.328125 3.58172 3.90985 0 8.32812 0H24.3281Z"
                stroke="#E5E7EB"
              />
              <path d="M25.3281 24H7.32812V8H25.3281V24Z" stroke="#E5E7EB" />
              <g clip-path="url(#clip0_247_11859)">
                <path
                  d="M16.8281 15.5V8.51875C16.8281 8.2375 17.0469 8 17.3281 8C21.1938 8 24.3281 11.1344 24.3281 15C24.3281 15.2812 24.0906 15.5 23.8094 15.5H16.8281ZM8.32812 16.5C8.32812 12.7094 11.1437 9.57187 14.7969 9.07187C15.0844 9.03125 15.3281 9.2625 15.3281 9.55313V17L20.2188 21.8906C20.4281 22.1 20.4125 22.4438 20.1719 22.6125C18.9469 23.4875 17.4469 24 15.8281 24C11.6875 24 8.32812 20.6438 8.32812 16.5ZM24.7781 17C25.0688 17 25.2969 17.2437 25.2594 17.5312C25.0187 19.2781 24.1781 20.8312 22.95 21.9781C22.7625 22.1531 22.4688 22.1406 22.2875 21.9563L17.3281 17H24.7781Z"
                  fill="#059669"
                />
              </g>
              <defs>
                <clipPath id="clip0_247_11859">
                  <path d="M7.32812 8H25.3281V24H7.32812V8Z" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>

          <div className="flex justify-between items-center mb-3 mt-5">
            <div>
              <p className="text-xs text-gray-500 mb-1">Available</p>
              <p className="text-xl font-bold text-green-600">
                $
                {statsData.available
                  ? statsData.available.toLocaleString()
                  : "0"}
              </p>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 mb-1">Utilized</p>
              <p className="text-xl font-bold text-red-500">
                $
                {statsData.totalBalance
                  ? statsData.totalBalance.toLocaleString()
                  : "0"}
              </p>
            </div>
          </div>
          <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden mb-1">
            <div
              className="bg-red-500 h-full"
              style={{ width: `${statsData.utilizedPercentage || 0}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0%</span>
            <span>{statsData.utilizedPercentage || 0}% Used</span>
            <span>100%</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-base font-medium text-gray-800">
              0% APR Ending
            </h2>
            <div className="flex items-center text-orange-500">
              <svg
                width="33"
                height="32"
                viewBox="0 0 33 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24.9844 0C29.4027 0 32.9844 3.58172 32.9844 8V24C32.9844 28.4183 29.4027 32 24.9844 32H8.98438C4.5661 32 0.984375 28.4183 0.984375 24V8C0.984375 3.58172 4.5661 0 8.98438 0H24.9844Z"
                  fill="#FEF3C7"
                />
                <path
                  d="M24.9844 0C29.4027 0 32.9844 3.58172 32.9844 8V24C32.9844 28.4183 29.4027 32 24.9844 32H8.98438C4.5661 32 0.984375 28.4183 0.984375 24V8C0.984375 3.58172 4.5661 0 8.98438 0H24.9844Z"
                  stroke="#E5E7EB"
                />
                <path d="M24.9844 24H8.98438V8H24.9844V24Z" stroke="#E5E7EB" />
                <g clip-path="url(#clip0_247_11884)">
                  <path
                    d="M16.9844 8C19.1061 8 21.1409 8.84285 22.6412 10.3431C24.1415 11.8434 24.9844 13.8783 24.9844 16C24.9844 18.1217 24.1415 20.1566 22.6412 21.6569C21.1409 23.1571 19.1061 24 16.9844 24C14.8626 24 12.8278 23.1571 11.3275 21.6569C9.82723 20.1566 8.98438 18.1217 8.98438 16C8.98438 13.8783 9.82723 11.8434 11.3275 10.3431C12.8278 8.84285 14.8626 8 16.9844 8ZM16.2344 11.75V16C16.2344 16.25 16.3594 16.4844 16.5687 16.625L19.5688 18.625C19.9125 18.8562 20.3781 18.7625 20.6094 18.4156C20.8406 18.0687 20.7469 17.6062 20.4 17.375L17.7344 15.6V11.75C17.7344 11.3344 17.4 11 16.9844 11C16.5687 11 16.2344 11.3344 16.2344 11.75Z"
                    fill="#D97706"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_247_11884">
                    <path d="M8.98438 8H24.9844V24H8.98438V8Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm">
            <div className="flex items-center p-4 border-b bg-red-50/30">
              <div className="flex-shrink-0 mr-4">
                <div className="bg-red-50 p-2 rounded-full">
                  <FaExclamationTriangle className="text-red-500" />
                </div>
              </div>
              <div className="flex-grow">
                <p className="text-sm">Less than 3 months</p>
              </div>
              <div className="flex-shrink-0">
                <span className="text-red-500 text-lg font-medium">1</span>
              </div>
            </div>
            <div className="flex items-center p-4 bg-yellow-50/30">
              <div className="flex-shrink-0 mr-4">
                <div className="bg-yellow-50 p-2 rounded-full">
                  <FaCalendarDay className="text-yellow-500" />
                </div>
              </div>
              <div className="flex-grow">
                <p className="text-sm">Less than 6 months</p>
              </div>
              <div className="flex-shrink-0">
                <span className="text-amber-500 text-lg font-medium">2</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showAlertCarousel && (
        <div className="mb-8 relative">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-9 flex items-start">
            <div className="mr-4 mt-0.5">
              <FaExclamationTriangle className="text-yellow-500 text-lg" />
            </div>
            <div className="flex-1">
              <p className="text-base font-medium text-gray-800 mb-1">
                {alertSlides[currentSlide].title}
              </p>
              <p className="text-sm text-gray-600">
                {alertSlides[currentSlide].content}
              </p>
            </div>
            <button
              className="text-gray-400 hover:text-gray-600 ml-4"
              onClick={handleCloseAlert}
            >
              <FaTimes />
            </button>
          </div>

          {/* Carousel Navigation */}
          <div className="flex justify-center mt-4 mb-2 relative">
            <div className="flex space-x-2">
              {alertSlides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`h-2 w-2 rounded-full ${
                    currentSlide === index ? "bg-yellow-500" : "bg-gray-300"
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
            <div className="absolute right-0 flex">
              <button
                onClick={prevSlide}
                className="bg-white rounded-l-lg border border-gray-200 w-10 h-8 flex items-center justify-center hover:bg-gray-50"
                aria-label="Previous slide"
              >
                <svg
                  width="10"
                  height="16"
                  viewBox="0 0 10 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M10 16H0V0H10V16Z" stroke="#E5E7EB" />
                  <path
                    d="M0.293945 7.29414C-0.0966797 7.68477 -0.0966797 8.31914 0.293945 8.70977L6.29395 14.7098C6.68457 15.1004 7.31895 15.1004 7.70957 14.7098C8.10019 14.3191 8.10019 13.6848 7.70957 13.2941L2.41582 8.00039L7.70645 2.70664C8.09707 2.31602 8.09707 1.68164 7.70645 1.29102C7.31582 0.900391 6.68145 0.900391 6.29082 1.29102L0.29082 7.29102L0.293945 7.29414Z"
                    fill="#4B5563"
                  />
                </svg>
              </button>
              <button
                onClick={nextSlide}
                className="ml-1 bg-white rounded-r-lg border-t border-r border-b border-gray-200 w-10 h-8 flex items-center justify-center hover:bg-gray-50 transform rotate-180"
                aria-label="Next slide"
              >
                <svg
                  width="10"
                  height="16"
                  viewBox="0 0 10 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M10 16H0V0H10V16Z" stroke="#E5E7EB" />
                  <path
                    d="M0.293945 7.29414C-0.0966797 7.68477 -0.0966797 8.31914 0.293945 8.70977L6.29395 14.7098C6.68457 15.1004 7.31895 15.1004 7.70957 14.7098C8.10019 14.3191 8.10019 13.6848 7.70957 13.2941L2.41582 8.00039L7.70645 2.70664C8.09707 2.31602 8.09707 1.68164 7.70645 1.29102C7.31582 0.900391 6.68145 0.900391 6.29082 1.29102L0.29082 7.29102L0.293945 7.29414Z"
                    fill="#4B5563"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 0% APR Ending */}

      {/* Your Credit Cards */}
      <div className="mb-6 mt-10 bg-white rounded-lg shadow-sm p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-base font-medium text-gray-800">
            Your Credit Cards
          </h2>
          <div className="flex items-center space-x-2">
            <button className="text-gray-400 hover:text-gray-600">
              <FaFilter className="text-sm" />
            </button>
            <button className="text-gray-400 hover:text-gray-600">
              <FaDownload className="text-sm" />
            </button>
            <div className="text-xs text-gray-500 border border-gray-200 rounded px-4 py-2 py-0.5 flex items-center">
              All Cards <FaArrowRight className="ml-1 text-xs" />
            </div>
          </div>
        </div>

        {/* Card list */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-50 text-gray-500 text-xs border-b">
                <tr>
                  <th className="px-4 py-3 text-left font-medium">
                    Institution
                  </th>
                  <th className="px-4 py-3 text-left font-medium">Card Name</th>
                  <th className="px-4 py-3 text-left font-medium">
                    Current Balance
                  </th>
                  <th className="px-4 py-3 text-left font-medium">
                    Balance Transfer
                  </th>
                  <th className="px-4 py-3 text-left font-medium">Utilized</th>
                  <th className="px-4 py-3 text-left font-medium">
                    Payment due date{" "}
                    <FaRegBell
                      className="text-gray-400 inline-block ml-1"
                      size={12}
                    />
                  </th>
                  <th className="px-4 py-3 text-left font-medium">
                    0% Ends{" "}
                    <FaRegBell
                      className="text-gray-400 inline-block ml-1"
                      size={12}
                    />
                  </th>
                  <th className="px-4 py-3 text-right font-medium"></th>
                </tr>
              </thead>
              <tbody>
                {/* Loading state for affiliate offers */}
                {isLoadingAffiliateOffers && (
                  <tr className="border-t border-gray-100">
                    <td colSpan={8} className="px-4 py-3 text-center">
                      <div className="flex justify-center items-center">
                        <div className="spinner animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                        <span className="text-sm text-gray-500">Loading partner offers...</span>
                      </div>
                    </td>
                  </tr>
                )}

                {/* Render interspersed cards and affiliate offers */}
                {/* Using 'figma' strategy: 1 card, affiliate, 2 cards, affiliate, 2 cards, etc. */}
                {!isLoadingAffiliateOffers && createInterspersedCardList('figma').map((item) =>
                  item.type === 'affiliate' ? (
                    <tr key={item.key} className="border-t border-gray-100">
                      <td colSpan={7} className="px-4 py-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded mr-3">
                              Sponsored
                            </span>
                            <div className="w-8 h-8 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                              {item.data.icon === "shield" ? (
                                <FaShieldAlt className="text-blue-500" />
                              ) : item.data.icon === "bank" ? (
                                <FaUniversity className="text-green-500" />
                              ) : (
                                <FaInfoCircle className="text-gray-500" />
                              )}
                            </div>
                            <div>
                              <p className="font-medium text-gray-800">
                                {item.data.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {item.data.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <button
                          className="text-blue-600 hover:text-blue-700 text-xs font-medium"
                          onClick={() => handleAffiliateClick(item.data)}
                        >
                          Visit Partner
                        </button>
                      </td>
                    </tr>
                  ) : item.data.sponsored ? (
                    <tr key={item.key} className="border-t border-gray-100">
                      <td colSpan={7} className="px-4 py-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded mr-3">
                              Sponsored
                            </span>
                            <div className="w-8 h-8 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                              <div
                                dangerouslySetInnerHTML={{ __html: item.data.svg }}
                              />
                            </div>
                            <div>
                              <p className="font-medium text-gray-800">
                                {item.data.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {item.data.type}
                              </p>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <button className="text-gray-400 hover:text-gray-600">
                          <FaTimes />
                        </button>
                      </td>
                    </tr>
                  ) : (
                    <tr key={item.key} className="border-t border-gray-100">
                      <td className="px-4 py-3 align-top">
                        <div className="flex items-start">
                          <BankIcon
                            iconKey={item.data.icon}
                            size="w-8 h-8"
                            className="mr-2 flex-shrink-0 mt-1"
                          />
                          <div>
                            <p className="font-medium text-gray-800 whitespace-nowrap">
                              {item.data.name} ••••{item.data.last_four}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {item.data.tags?.map((tag: string, index: number) => (
                                <span
                                  key={index}
                                  className={`text-xs px-2 py-0.5 rounded-full ${
                                    item.data.tagColors && item.data.tagColors[tag]
                                      ? `bg-${item.data.tagColors[tag]}-100 text-${item.data.tagColors[tag]}-700`
                                      : tag === "Travel"
                                        ? "bg-blue-100 text-blue-700"
                                        : tag === "Dining"
                                          ? "bg-purple-100 text-purple-700"
                                          : tag === "Points"
                                            ? "bg-green-100 text-green-700"
                                            : tag === "Hotels"
                                              ? "bg-orange-100 text-orange-700"
                                              : tag === "Premium"
                                                ? "bg-red-100 text-red-700"
                                                : tag === "Cashback"
                                                  ? "bg-green-100 text-green-700"
                                                  : tag === "Rotating"
                                                    ? "bg-yellow-100 text-yellow-700"
                                                    : tag === "Miles"
                                                      ? "bg-sky-100 text-sky-700" // Adjusted Miles color
                                                      : "bg-gray-100 text-gray-700"
                                  }`}
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 align-top">
                        <p className="font-medium text-gray-800">{item.data.type}</p>
                      </td>
                      <td className="px-4 py-3 font-medium text-gray-800 align-top">
                        {item.data.current_balance}
                      </td>
                      <td className="px-4 py-3 font-medium text-gray-800 align-top">
                        {item.data.balance_transfer && item.data.balance_transfer !== "" ? (
                          <span className="text-blue-600">
                            ${parseFloat(item.data.balance_transfer).toLocaleString()}
                          </span>
                        ) : (
                          <span className="text-gray-400 text-xs">-</span>
                        )}
                      </td>
                      <td className="px-4 py-3 align-top">
                        <div className="flex items-center whitespace-nowrap">
                          <span className="text-xs text-gray-500 mr-2">
                            {item.data.utilizedPercentage || 0}%
                          </span>
                          <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className={`h-full ${
                                item.data.utilizedPercentage === 0
                                  ? "bg-gray-200" // Keep it gray if 0
                                  : item.data.utilizedPercentage &&
                                      item.data.utilizedPercentage <= 20
                                    ? "bg-green-500"
                                    : item.data.utilizedPercentage &&
                                        item.data.utilizedPercentage <= 50
                                      ? "bg-yellow-500"
                                      : "bg-red-500"
                              }`}
                              style={{
                                width: `${item.data.utilizedPercentage || 0}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 align-top">
                        {item.data.noBalance || item.data.isQuilttCard ? (
                          <div>
                            <p className="text-xs font-medium text-gray-400">
                              {item.data.isQuilttCard ? "Connected Account" : "N/A"}
                            </p>
                            <p className="text-xs text-gray-500 flex items-center">
                              {item.data.isQuilttCard ? "Real-time sync" : "No balance"}{" "}
                              <FaRegBell
                                className="ml-1 text-gray-400"
                                size={10}
                              />
                            </p>
                            <p className="text-xs text-gray-500">
                              Amount: {item.data.dueAmount}
                            </p>
                          </div>
                        ) : (
                          <div className="cursor-pointer hover:bg-gray-50 p-1 rounded transition-colors">
                            <div className="flex items-center justify-between">
                              <p
                                className={`text-xs font-medium whitespace-nowrap ${
                                  item.data.daysLeft && item.data.daysLeft <= 5
                                    ? "text-red-500"
                                    : item.data.daysLeft && item.data.daysLeft <= 15
                                      ? "text-orange-500"
                                      : "text-gray-800"
                                }`}
                              >
                                {item.data.paymentDueDate}
                              </p>
                            </div>
                            <p className="text-xs text-gray-500 whitespace-nowrap">
                              {item.data.daysLeft || 0} days left
                              {item.data.issuer === "chase" &&
                              item.data.daysLeft &&
                              item.data.daysLeft <= 5 ? (
                                <FaRegBell
                                  className="ml-1 text-green-500"
                                  size={10}
                                />
                              ) : null}
                              {item.data.issuer === "citi" &&
                              item.data.daysLeft &&
                              item.data.daysLeft <= 15 ? (
                                <FaRegBell
                                  className="ml-1 text-gray-400"
                                  size={10}
                                />
                              ) : null}
                              {item.data.issuer === "amex" && item.data.daysLeft ? (
                                <FaRegBell
                                  className="ml-1 text-green-500"
                                  size={10}
                                />
                              ) : null}
                              {item.data.issuer === "discover" && item.data.daysLeft ? (
                                <FaRegBell
                                  className="ml-1 text-gray-400"
                                  size={10}
                                />
                              ) : null}
                            </p>
                            <p className="text-xs text-gray-500">
                              Amount: {item.data.dueAmount}
                            </p>
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3 align-top whitespace-nowrap">
                        {item.data.isQuilttCard ? (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
                            Live Data
                          </span>
                        ) : item.data.status === "expired" &&
                        item.data.aprEndDate === "Expired" ? (
                          <span className="text-xs border border-red-200 bg-red-50 text-red-600 px-2 py-0.5 rounded">
                            Expired
                          </span>
                        ) : item.data.aprEndDate ? (
                          <div className="cursor-pointer hover:bg-gray-50 p-1 rounded transition-colors">
                            <div className="flex items-center justify-between">
                              <span
                                className={`text-xs px-2 py-0.5 rounded ${
                                  item.data.aprEndDate.includes("left")
                                    ? "bg-green-100 text-green-700"
                                    : "bg-gray-100 text-gray-700"
                                }`}
                              >
                                {item.data.aprEndDate}
                              </span>
                            </div>

                            {/* Specific for Capital One and Amex to have bell icon */}
                            {(item.data.issuer === "capitalone" ||
                              item.data.issuer === "amex") && (
                              <div className="mt-1 flex justify-end">
                                <FaRegBell
                                  className="text-green-500"
                                  size={10}
                                />
                              </div>
                            )}
                          </div>
                        ) : null}
                      </td>
                      <td className="px-4 py-3 align-top">
                        {/* <div className="flex gap-1 justify-end items-center whitespace-nowrap">
                          {card.status === "confirm" ? (
                            <button className="text-xs bg-[#16c66c] hover:bg-[#238e4e] text-white rounded px-3 py-1 flex items-center">
                              <FaCheck className="mr-1" size={10} /> Confirm
                            </button>
                          ) : card.status === "expired" ? (
                            <>
                              <button className="text-gray-400 hover:text-gray-600 p-0.5">
                                <FaRegCalendarAlt size={14} />
                              </button>
                              <button className="text-gray-400 hover:text-gray-600 p-0.5">
                                <FaRegBell size={14} />
                              </button>
                            </>
                          ) : card.status === "good" &&
                            (card.issuer === "amex" ||
                              card.issuer === "capitalone") ? (
                            <>
                              <button className="text-green-500 hover:text-green-700 p-0.5">
                                <FaRegCalendarAlt size={14} />
                              </button>
                              <button className="text-green-500 hover:text-green-700 p-0.5">
                                <FaRegBell size={14} />
                              </button>
                            </>
                          ) : (
                            <button className="text-xs border border-gray-300 hover:bg-gray-50 text-gray-700 rounded px-3 py-1">
                              Details{" "}
                            </button>
                          )}
                        </div> */}
                      </td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>
          <div className="p-3 border-t text-xs text-gray-500 flex justify-between items-center">
            <div>
              Showing {cards.filter((card) => !card.sponsored).length} of{" "}
              {cards.filter((card) => !card.sponsored).length} cards
            </div>
            <div className="flex items-center space-x-2">
              <button
                className="border border-gray-200 rounded flex items-center px-3 py-1.5 bg-[#16c66c] text-white"
                onClick={handleAddCardClick}
              >
                <FaPlus className="mr-1.5 text-xs" /> Add Credit Card
              </button>
              <button className="border border-gray-300 hover:bg-gray-100 text-gray-500 rounded px-3 py-1.5 text-xs">
                &lt; Prev
              </button>
              <button className="border border-gray-300 hover:bg-gray-100 text-gray-500 rounded px-3 py-1.5 text-xs">
                Next &gt;
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* AI-Powered Recommendations */}
      {isLoadingRecommendations ? (
        // Loading state for recommendations
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center mr-2">
              <svg className="w-4 h-4 text-green-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 10V3L4 14H11V21L20 10H13Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h2 className="text-base font-medium text-gray-800">
              AI-Powered Recommendations
            </h2>
          </div>
          <div className="flex justify-center items-center h-32">
            <div className="spinner animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500"></div>
          </div>
        </div>
      ) : cards.length > 0 && recommendations.length > 0 ? (
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center mr-2">
              <svg className="w-4 h-4 text-green-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 10V3L4 14H11V21L20 10H13Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h2 className="text-base font-medium text-gray-800">
              AI-Powered Recommendations
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-4">
            {recommendations.map((rec, index) => (
              <div
                key={rec.id || index}
                className="bg-white rounded-lg shadow-sm p-4 border border-gray-100"
              >
                <div className="flex items-start mb-3">
                  <div
                    className={`w-8 h-8 rounded-md flex items-center justify-center mr-3 flex-shrink-0 ${
                      rec.icon === "utensils" || rec.type?.toLowerCase().includes("dining")
                        ? "bg-yellow-100"
                        : rec.icon === "exclamation-triangle" || rec.type?.toLowerCase().includes("apr")
                        ? "bg-red-100"
                        : rec.icon === "exchange-alt" || rec.type?.toLowerCase().includes("transfer")
                        ? "bg-blue-100"
                        : "bg-gray-100"
                    }`}
                  >
                    {rec.icon === "utensils" || rec.type?.toLowerCase().includes("dining") ? (
                      <FaMoneyBillWave className="text-yellow-600 text-sm" />
                    ) : rec.icon === "plane" || rec.type?.toLowerCase().includes("travel") ? (
                      <FaPlane className="text-blue-600 text-sm" />
                    ) : rec.icon === "gas-pump" || rec.type?.toLowerCase().includes("gas") ? (
                      <FaGasPump className="text-orange-600 text-sm" />
                    ) : rec.icon === "credit-card" || rec.type?.toLowerCase().includes("groceries") ? (
                      <FaCreditCard className="text-green-600 text-sm" />
                    ) : rec.icon === "exclamation-triangle" || rec.type?.toLowerCase().includes("apr") ? (
                      <FaExclamationTriangle className="text-red-600 text-sm" />
                    ) : rec.icon === "exchange-alt" || rec.type?.toLowerCase().includes("transfer") ? (
                      <FaExchangeAlt className="text-blue-600 text-sm" />
                    ) : (
                      <FaInfoCircle className="text-gray-600 text-sm" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm text-gray-800 mb-1">{rec.title}</p>
                    <p className="text-xs text-gray-600 leading-relaxed">{rec.description}</p>
                  </div>
                </div>
                {rec.actionText && (
                  <button
                    className={`text-xs font-medium ${
                      rec.actionVariant === "warning"
                        ? "text-red-600 hover:text-red-700"
                        : "text-blue-600 hover:text-blue-700"
                    }`}
                  >
                    {rec.actionText}
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      ) : cards.length === 0 ? (
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center mr-2">
              <svg className="w-4 h-4 text-green-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 10V3L4 14H11V21L20 10H13Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h2 className="text-base font-medium text-gray-800">
              AI-Powered Recommendations
            </h2>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center">
            <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-gray-400"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13 10V3L4 14H11V21L20 10H13Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <p className="font-medium text-base mb-2 text-center">
              Unlock Personalized AI Recommendations
            </p>
            <p className="text-gray-500 text-sm mb-4 text-center max-w-md">
              Sync your cards to get personalized AI-powered recommendations
              based on spending patterns and card benefits.
            </p>
            <button
              className="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
              onClick={() => setShowQuiltt(true)}
            >
              Sync Your Cards
            </button>
            <p className="text-xs text-gray-500 mt-2 text-center">
              We'll analyze your cards and suggest the best ways to maximize rewards
            </p>
          </div>
        </div>
      ) : null}

      {/* Spending Insights Section - Only show when cards are linked and we have insights */}
      {cards.length > 0 && spendingInsights.length > 0 && (
        <div className="mb-6">
          <div className="grid md:grid-cols-3 gap-4">
            {spendingInsights.map((insight: any, index: number) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
                <div className="flex items-start mb-3">
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center mr-3 flex-shrink-0 ${
                    insight.category.toLowerCase() === 'dining' ? 'bg-yellow-100' :
                    insight.category.toLowerCase() === 'travel' ? 'bg-blue-100' :
                    insight.category.toLowerCase() === 'groceries' ? 'bg-green-100' :
                    insight.category.toLowerCase() === 'gas' ? 'bg-orange-100' :
                    'bg-purple-100'
                  }`}>
                    {insight.category.toLowerCase() === 'dining' ? (
                      <FaMoneyBillWave className="text-yellow-600 text-sm" />
                    ) : insight.category.toLowerCase() === 'travel' ? (
                      <FaPlane className="text-blue-600 text-sm" />
                    ) : insight.category.toLowerCase() === 'groceries' ? (
                      <FaShoppingCart className="text-green-600 text-sm" />
                    ) : insight.category.toLowerCase() === 'gas' ? (
                      <FaGasPump className="text-orange-600 text-sm" />
                    ) : (
                      <FaChartPie className="text-purple-600 text-sm" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm text-gray-800 mb-1">
                      {insight.category} Spending
                    </p>
                    <div className="mb-2">
                      <span className="bg-purple-100 text-purple-700 text-xs px-2 py-0.5 rounded-full">
                        {Math.round(insight.percentage * 100)}% of income
                      </span>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-gray-600 leading-relaxed mb-3">
                  {insight.insight}
                </p>
                <p className="text-xs text-blue-600 font-medium">
                  {insight.recommendation}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Spending Insights */}
      <div className="mb-6 md:grid-cols-3 grid gap-4">
        {isLoadingInsights ? (
          // Loading state for insights
          <div className="col-span-3 flex justify-center items-center h-32">
            <div className="spinner animate-spin rounded-full h-8 w-8 border-t-2 border-purple-500"></div>
          </div>
        ) : (
          spendingInsights.length > 0 &&
          // Map through spending insights if available
          spendingInsights.map((insight, index) => (
            <div
              key={insight.id || index}
              className="bg-white rounded-lg shadow-sm p-4"
            >
              <div className="flex items-center mb-2">
                <div
                  className={`w-7 h-7 rounded-full bg-${insight.iconBg || "purple"}-50 flex items-center justify-center mr-2`}
                >
                  {insight.icon === "utensils" ? (
                    <FaMoneyBillWave
                      className={`text-${insight.iconBg || "purple"}-500 text-sm`}
                    />
                  ) : insight.icon === "plane" ? (
                    <FaPlane
                      className={`text-${insight.iconBg || "blue"}-500 text-sm`}
                    />
                  ) : insight.icon === "shopping-cart" ? (
                    <FaShoppingCart
                      className={`text-${insight.iconBg || "orange"}-500 text-sm`}
                    />
                  ) : (
                    <FaChartPie
                      className={`text-${insight.iconBg || "purple"}-500 text-sm`}
                    />
                  )}
                </div>
                <p className="font-medium text-sm">{insight.title}</p>
              </div>
              <p className="text-xs text-gray-600 mb-2">
                {insight.description}
              </p>
              {insight.amount && insight.percentage && (
                <div className="flex justify-between items-center text-xs mb-2">
                  <span className="text-gray-500">
                    Amount: ${insight.amount.toLocaleString()}
                  </span>
                  <span className="bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">
                    {insight.percentage}% of spending
                  </span>
                </div>
              )}
              {insight.actionText && (
                <button
                  className={`text-xs text-purple-600 hover:text-purple-700 font-medium`}
                >
                  {insight.actionText}
                </button>
              )}
            </div>
          ))
        )}
      </div>

      {/* Partner Offers Section */}
      {!isLoadingAffiliateOffers && affiliateOffers.length > 0 && (
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <h2 className="text-base font-medium text-gray-800">
              Partner Offers
            </h2>
            <span className="ml-2 bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full">
              Sponsored
            </span>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            {affiliateOffers.map((offer) => (
              <div
                key={offer.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                    {offer.icon === "shield" ? (
                      <FaShieldAlt className="text-blue-500 text-lg" />
                    ) : offer.icon === "bank" ? (
                      <FaUniversity className="text-green-500 text-lg" />
                    ) : (
                      <FaInfoCircle className="text-gray-500 text-lg" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-800">{offer.name}</h3>
                    <p className="text-xs text-gray-500">{offer.category}</p>
                  </div>
                  <span className="bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded">
                    Sponsored
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                  {offer.description}
                </p>
                <div className="flex flex-wrap gap-1 mb-4">
                  {offer.categories.map((category, index) => (
                    <span
                      key={index}
                      className="text-xs px-2 py-0.5 rounded-md bg-gray-100 text-gray-600"
                    >
                      {category}
                    </span>
                  ))}
                </div>
                <button
                  className="w-full py-2 text-center text-blue-600 bg-white border border-blue-600 rounded-md text-sm font-medium hover:bg-blue-50 transition-colors"
                  onClick={() => handleAffiliateClick(offer)}
                >
                  Visit Partner
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional Resources Section */}
      <div className="mb-6">
        <h2 className="text-base font-medium text-gray-800 mb-3">
          Additional Resources
        </h2>

        <div className="grid md:grid-cols-2 gap-4">
          {/* Calendar */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex justify-between items-center mb-2">
              <p className="font-medium text-sm">30-Day Calendar</p>
              <div className="flex items-center text-xs">
                <button className="p-1 text-gray-400 hover:text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <span className="mx-2">{month}</span>
                <button className="p-1 text-gray-400 hover:text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="grid grid-cols-7 gap-1 text-center text-xs uppercase font-medium text-gray-400 mb-1">
              <div>Sun</div>
              <div>Mon</div>
              <div>Tue</div>
              <div>Wed</div>
              <div>Thu</div>
              <div>Fri</div>
              <div>Sat</div>
            </div>
            <div className="grid grid-cols-7 gap-1 text-center text-xs">
              <div className="p-1 text-gray-300">28</div>
              <div className="p-1 text-gray-300">29</div>
              <div className="p-1 text-gray-300">30</div>
              <div className="p-2">1</div>
              <div className="p-2">2</div>
              <div className="p-2">3</div>
              <div className="p-2">4</div>
              <div className="p-2">5</div>
              <div className="p-2">6</div>
              <div className="p-2">7</div>
              <div className="p-2">8</div>
              <div className="p-2">9</div>
              <div className="p-2">10</div>
              <div className="p-2">11</div>
              <div className="p-2">12</div>
              <div className="p-2">13</div>
              <div className="p-2">14</div>
              <div className="p-2 relative">
                15
                <div className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-red-500 rounded-full"></div>
              </div>
              <div className="p-2">16</div>
              <div className="p-2">17</div>
              <div className="p-2">18</div>
              <div className="p-2">19</div>
              <div className="p-2">20</div>
              <div className="p-2">21</div>
              <div className="p-2">22</div>
              <div className="p-2">23</div>
              <div className="p-2 relative">
                24
                <div className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-red-500 rounded-full"></div>
              </div>
              <div className="p-2">25</div>
              <div className="p-2">26</div>
              <div className="p-2">27</div>
              <div className="p-2">28</div>
              <div className="p-2">29</div>
              <div className="p-2">30</div>
              <div className="p-2">31</div>
              <div className="p-2 text-gray-300">1</div>
            </div>
            <div className="border-t mt-3 pt-3 flex flex-col sm:flex-row justify-start gap-3 text-xs">
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-red-500 mr-1.5"></div>
                <span className="text-gray-600">Payment Due</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-green-500 mr-1.5"></div>
                <span className="text-gray-600">0% APR Ending</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-1.5"></div>
                <span className="text-gray-600">Balance Transfer</span>
              </div>
            </div>
          </div>

          {/* Balance Transfer and Potential Savings */}
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <div className="flex justify-between items-center mb-1">
                <p className="font-medium text-sm">Balance Transfer</p>
                <span className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full font-medium">
                  Recommended
                </span>
              </div>

              <p className="text-xs text-gray-500 mb-3">
                Find cards with 0% APR offers and calculate your savings.
              </p>
              <p className="font-medium text-sm text-gray-800">
                Potential Interest Savings
              </p>
              <p className="text-2xl font-bold text-green-600 mb-0.5">$420</p>
              <p className="text-xs text-gray-500 mb-3">
                By transferring high-interest balances
              </p>
              <button
                onClick={handleFindBalanceTransferCards}
                className="w-full bg-[#16c66c] hover:bg-[#238e4e] text-white text-sm py-2 rounded-md text-center font-medium"
              >
                Find 0% APR Cards
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quiltt Modal */}
      {showQuiltt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="p-4">
            <QuilttContainer
              onSuccess={handleQuilttSuccess}
              onExit={handleQuilttExit}
              onError={handleQuilttError}
            />
          </div>
        </div>
      )}

      {/* Balance Transfer Modal */}
      {showBalanceTransferModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  0% APR Balance Transfer Cards
                </h2>
                <p className="text-sm text-gray-500 mt-1">
                  Cards with available balance transfer amounts
                </p>
              </div>
              <button
                onClick={handleCloseBalanceTransferModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FaTimes size={20} />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {isLoadingBalanceTransfer ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : balanceTransferCards.length === 0 ? (
                <div className="text-center py-12">
                  <FaCreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No Balance Transfer Cards Found
                  </h3>
                  <p className="text-gray-500">
                    You don't have any cards with available balance transfer amounts.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {balanceTransferCards.map((card: any) => (
                    <div
                      key={card.id}
                      className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <BankIcon iconKey={card.icon} size="w-6 h-6" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">{card.name}</h3>
                            <p className="text-sm text-gray-500">•••• {card.last_four}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Balance Transfer</p>
                          <p className="font-semibold text-green-600">
                            ${parseFloat(card.balance_transfer).toLocaleString()}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Current Balance</p>
                          <p className="font-medium">${parseFloat(card.current_balance).toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Credit Limit</p>
                          <p className="font-medium">${parseFloat(card.credit_limit).toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Utilization</p>
                          <p className="font-medium">{card.utilizedPercentage}%</p>
                        </div>
                        <div>
                          <p className="text-gray-500">APR End Date</p>
                          <p className="font-medium text-orange-600">{card.aprEndDate}</p>
                        </div>
                      </div>

                      {card.tags && card.tags.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {card.tags.map((tag: string, index: number) => (
                            <span
                              key={index}
                              className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={handleCloseBalanceTransferModal}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="text-center text-xs text-gray-500 py-4">
        © 2023 Stackeasy. All rights reserved.
        <div className="flex justify-center gap-4 mt-2">
          <a href="#" className="hover:text-gray-700">
            Affiliate Disclosure
          </a>
          <a href="#" className="hover:text-gray-700">
            Privacy Policy
          </a>
          <a href="#" className="hover:text-gray-700">
            Terms of Service
          </a>
          <a href="#" className="hover:text-gray-700">
            Contact Support
          </a>
        </div>
      </div>
    </div>
  );
};

export default HomeWithCard;
