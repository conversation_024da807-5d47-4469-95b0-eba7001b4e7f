import { lazy } from "react";

export const CurrencyCell = lazy(() => import("./CurrencyCell"));
export const DefaultCell = lazy(() => import("./DefaultCell"));
export const EditableStatusCell = lazy(() => import("./EditableStatusCell"));
export const EditableTextCell = lazy(() => import("./EditableTextCell"));
export const FileCell = lazy(() => import("./FileCell"));
export const ImageCell = lazy(() => import("./ImageCell"));
export const JoinCell = lazy(() => import("./JoinCell"));
export const ListCell = lazy(() => import("./ListCell"));
export const NoteCell = lazy(() => import("./NoteCell"));
export const StatusCell = lazy(() => import("./StatusCell"));
export const TruncatedCell = lazy(() => import("./TruncatedCell"));
