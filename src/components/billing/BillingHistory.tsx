import React from "react";
import { BillingTransaction, BillingHistoryPagination } from "@/services/api";
import { FaFileInvoice, FaDownload, FaSpinner } from "react-icons/fa";

interface BillingHistoryProps {
  transactions: BillingTransaction[];
  pagination: BillingHistoryPagination;
  isLoading: boolean;
  onLoadMore: () => void;
}

const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase() || "USD",
  }).format(amount);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const getStatusBadge = (status: string) => {
  switch (status.toLowerCase()) {
    case "succeeded":
      return (
        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
          Paid
        </span>
      );
    case "failed":
      return (
        <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
          Failed
        </span>
      );
    case "pending":
      return (
        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
          Pending
        </span>
      );
    default:
      return (
        <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
          {status}
        </span>
      );
  }
};

const BillingHistory: React.FC<BillingHistoryProps> = ({
  transactions,
  pagination,
  isLoading,
  onLoadMore,
}) => {
  if (transactions.length === 0 && !isLoading) {
    return (
      <div className="text-center py-10 bg-gray-50 rounded-lg border border-gray-200">
        <FaFileInvoice className="mx-auto h-10 w-10 text-gray-400 mb-2" />
        <p className="text-gray-500">No billing history found</p>
        <p className="text-sm text-gray-400 mt-1">
          Your payment history will appear here
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
              >
                Date
              </th>
              <th
                scope="col"
                className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
              >
                Description
              </th>
              <th
                scope="col"
                className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
              >
                Payment Method
              </th>
              <th
                scope="col"
                className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
              >
                Amount
              </th>
              <th
                scope="col"
                className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
              >
                Status
              </th>
              <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                <span className="sr-only">Receipt</span>
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {transactions.map((transaction) => (
              <tr key={transaction.id}>
                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-6">
                  {formatDate(transaction.create_at)}
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  {transaction.description}
                  {transaction.plan_name && transaction.billing_cycle && (
                    <span className="block text-xs text-gray-400">
                      {transaction.plan_name.charAt(0).toUpperCase() +
                        transaction.plan_name.slice(1)}{" "}
                      Plan ({transaction.billing_cycle})
                    </span>
                  )}
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  {transaction.card_brand && transaction.last_four ? (
                    <>
                      {transaction.card_brand.charAt(0).toUpperCase() +
                        transaction.card_brand.slice(1)}{" "}
                      •••• {transaction.last_four}
                    </>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900 font-medium">
                  {formatCurrency(transaction.amount, transaction.currency)}
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm">
                  {getStatusBadge(transaction.status)}
                </td>
                <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                  {transaction.receipt_url && (
                    <a
                      href={transaction.receipt_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <FaDownload title="Download Receipt" />
                      <span className="sr-only">Download Receipt</span>
                    </a>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {pagination.hasMore && (
        <div className="mt-4 text-center">
          <button
            onClick={onLoadMore}
            disabled={isLoading}
            className="px-4 py-2 text-sm text-blue-600 bg-white border border-blue-300 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <FaSpinner className="inline-block animate-spin mr-2" />
                Loading...
              </>
            ) : (
              `Load More (${pagination.totalCount - transactions.length} remaining)`
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default BillingHistory;
