import React, { useState } from "react";
import {
  FaCreditCard,
  FaTrash,
  FaStar,
  FaRegStar,
  FaSpinner,
  FaEdit,
  FaCheckCircle,
  FaExclamationTriangle,
} from "react-icons/fa";
import { PaymentMethod, billingApi } from "@/services/api";
import { toast } from "react-toastify";

interface PaymentMethodsListProps {
  paymentMethods: PaymentMethod[];
  onRefresh: () => void;
  isRefreshing?: boolean;
}

const getCardIcon = (brand: string) => {
  switch (brand.toLowerCase()) {
    case "visa":
      return { icon: "💳", color: "bg-blue-100 text-blue-600", name: "Visa" };
    case "mastercard":
      return { icon: "💳", color: "bg-red-100 text-red-600", name: "Mastercard" };
    case "amex":
      return { icon: "💳", color: "bg-blue-100 text-blue-600", name: "AMEX" };
    case "discover":
      return { icon: "💳", color: "bg-orange-100 text-orange-600", name: "Discover" };
    default:
      return { icon: "💳", color: "bg-gray-100 text-gray-600", name: "Card" };
  }
};

const getCardStatus = (method: PaymentMethod) => {
  // You can customize this logic based on your actual sync status
  // For now, we'll use is_default as a proxy for sync status
  if (method.is_default) {
    return {
      status: "Synced",
      color: "bg-green-100 text-green-700",
      icon: <FaCheckCircle className="h-3 w-3" />
    };
  } else {
    return {
      status: "Not synced",
      color: "bg-orange-100 text-orange-700",
      icon: <FaExclamationTriangle className="h-3 w-3" />
    };
  }
};

const PaymentMethodsList: React.FC<PaymentMethodsListProps> = ({
  paymentMethods,
  onRefresh,
}) => {
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<number | null>(null);

  const handleDelete = async (id: number) => {
    if (
      window.confirm("Are you sure you want to delete this payment method?")
    ) {
      setDeletingId(id);
      try {
        await billingApi.deletePaymentMethod(id);
        toast.success("Payment method deleted successfully");
        onRefresh();
      } catch (error: any) {
        toast.error(error.message || "Failed to delete payment method");
      } finally {
        setDeletingId(null);
      }
    }
  };

  const handleSetDefault = async (id: number) => {
    setSettingDefaultId(id);
    try {
      await billingApi.setDefaultPaymentMethod(id);
      toast.success("Default payment method updated");
      onRefresh();
    } catch (error: any) {
      toast.error(error.message || "Failed to update default payment method");
    } finally {
      setSettingDefaultId(null);
    }
  };

  if (paymentMethods.length === 0) {
    return (
      <div className="text-center py-6 bg-gray-50 rounded-lg border border-gray-200">
        <FaCreditCard className="mx-auto h-10 w-10 text-gray-400 mb-2" />
        <p className="text-gray-500">No payment methods found</p>
        <p className="text-sm text-gray-400 mt-1">
          Add a card to manage your subscription
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Your Credit Cards</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {paymentMethods.map((method) => {
              const cardInfo = getCardIcon(method.card_brand);
              const statusInfo = getCardStatus(method);

              return (
                <div
                  key={method.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    {/* Card Icon */}
                    <div className={`w-12 h-8 rounded-md flex items-center justify-center ${cardInfo.color}`}>
                      <span className="text-lg font-bold">
                        {cardInfo.name === "Visa" ? "VISA" :
                         cardInfo.name === "Mastercard" ? "MC" :
                         cardInfo.name === "AMEX" ? "AMEX" : "CARD"}
                      </span>
                    </div>

                    {/* Card Details */}
                    <div>
                      <div className="font-medium text-gray-900">
                        {cardInfo.name} Platinum •••• {method.last_four}
                      </div>
                      <div className="text-sm text-gray-500">
                        Exp {method.expiry_month.toString().padStart(2, '0')}/{method.expiry_year}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-2">
                    {/* Status Badge */}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
                      {statusInfo.icon}
                      <span className="ml-1">{statusInfo.status}</span>
                    </span>

                    {/* Action Buttons - In Row */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSetDefault(method.id)}
                        disabled={settingDefaultId === method.id || method.is_default}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium disabled:text-gray-400"
                      >
                        {settingDefaultId === method.id ? (
                          <FaSpinner className="animate-spin" />
                        ) : (
                          "Edit"
                        )}
                      </button>

                      {!method.is_default && (
                        <button
                          onClick={() => handleDelete(method.id)}
                          disabled={deletingId === method.id}
                          className="text-gray-600 hover:text-red-600 text-sm font-medium disabled:text-gray-400"
                        >
                          {deletingId === method.id ? (
                            <FaSpinner className="animate-spin" />
                          ) : (
                            "Remove"
                          )}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethodsList;
