import React, { createContext, useContext, useEffect, useState } from "react";
import { loadStripe, Stripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import { billingApi } from "@/services/api";

interface StripeContextType {
  publishableKey: string | null;
  stripePromise: Promise<Stripe | null> | null;
  isLoading: boolean;
}

const StripeContext = createContext<StripeContextType>({
  publishableKey: null,
  stripePromise: null,
  isLoading: true,
});

export const useStripe = () => useContext(StripeContext);

interface StripeProviderProps {
  children: React.ReactNode;
}

export const StripeProvider: React.FC<StripeProviderProps> = ({ children }) => {
  const [publishableKey, setPublishableKey] = useState<string | null>(null);
  const [stripePromise, setStripePromise] =
    useState<Promise<Stripe | null> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeStripe = async () => {
      try {
        // Fetch publishable key from backend
        const { publishableKey } = await billingApi.getSubscriptionPlans();
        setPublishableKey(publishableKey);

        // Initialize Stripe with the key
        const stripePromise = loadStripe(publishableKey);
        setStripePromise(stripePromise);
      } catch (error) {
        console.error("Failed to initialize Stripe:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeStripe();
  }, []);

  // Don't render anything until Stripe is initialized
  if (isLoading || !stripePromise) {
    return (
      <div className="flex justify-center items-center py-10">
        Loading payment system...
      </div>
    );
  }

  return (
    <StripeContext.Provider
      value={{ publishableKey, stripePromise, isLoading }}
    >
      <Elements stripe={stripePromise}>{children}</Elements>
    </StripeContext.Provider>
  );
};

export default StripeProvider;
