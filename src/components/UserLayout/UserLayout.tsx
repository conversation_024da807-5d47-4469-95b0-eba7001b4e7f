import React, { ReactNode, useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  FaCreditCard,
  FaUniversity,
  FaPercent,
  FaGift,
  FaBell,
  FaCog,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaChevronDown,
  FaChevronRight,
  FaPlus,
  FaCalendarAlt,
  FaArrowLeft,
  FaChartLine,
} from "react-icons/fa";
import QuilttContainer from "@/components/Credit/QuilttContainer";
import { authApi } from "../../services/api";
import { GuardLogo } from "@/assets/images";

interface UserLayoutProps {
  children: ReactNode;
}

// Placeholder for a simple dropdown component - can be expanded later
const SimpleDropdown: React.FC<{ text: string }> = ({ text }) => (
  <button className="bg-white border border-gray-300 text-gray-700 text-sm px-3 py-1.5 rounded-md flex items-center hover:bg-gray-50">
    {text}
    <FaChevronDown className="w-3 h-3 ml-2" />
  </button>
);

const UserLayout: React.FC<UserLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [rewardsSubMenuOpen, setRewardsSubMenuOpen] = useState(
    location.pathname.includes("/user/rewards")
  );
  const [showQuilttModal, setShowQuilttModal] = useState(false);
  const [userName, setUserName] = useState<string>("");
  const [userFullName, setUserFullName] = useState<string>("");

  useEffect(() => {
    // Get user data from auth service
    const user = authApi.getCurrentUser();
    if (user && user.fullName) {
      setUserName(user.fullName.split(" ")[0]); // Get first name only
      setUserFullName(user.fullName);
    }
  }, []);

  // Handle user logout
  const handleLogout = () => {
    authApi.logout(); // Clear auth data from localStorage
    navigate("/user/auth/login"); // Redirect to login page
  };

  const isActive = (path: string) => {
    return location.pathname.includes(path)
      ? "bg-blue-50 text-blue-600"
      : "text-gray-600 hover:bg-gray-100";
  };

  const isExactActive = (path: string) => {
    return location.pathname === path
      ? "bg-blue-50 text-blue-600"
      : "text-gray-600 hover:bg-gray-100";
  };

  const renderDynamicTopBarActions = () => {
    const { pathname } = location;

    if (pathname === "/user/credit") {
      return (
        <button
          onClick={() => setShowQuilttModal(true)}
          className="bg-[#16c66c] text-white text-sm px-3 py-1.5 rounded-md flex items-center hover:bg-[#238e4e]"
        >
          <FaPlus className="mr-1.5" /> Add Credit Card
        </button>
      );
    }
    if (pathname === "/user/bank") {
      return (
        <button className="bg-[#16c66c] text-white text-sm px-3 py-1.5 rounded-md flex items-center hover:bg-[#238e4e]">
          <FaPlus className="mr-1.5" /> Add bank account
        </button>
      );
    }
    if (pathname === "/user/affiliate") {
      return <SimpleDropdown text="Last 7 days" />;
    }
    if (pathname === "/user/rewards") {
      return (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigate("/user/rewards/bonus-tracker")}
            className="bg-yellow-500 hover:bg-yellow-600 text-white text-sm px-3 py-1.5 rounded-md flex items-center"
          >
            <FaChartLine className="mr-1.5" /> Bonus Tracker
          </button>
          <SimpleDropdown text="Last 7 days" />
          <button className="bg-[#16c66c] hover:bg-[#238e4e] text-white text-sm px-3 py-1.5 rounded-md">
            Finish
          </button>
        </div>
      );
    }
    if (pathname.startsWith("/user/rewards/cards/")) {
      return (
        <div className="flex items-center space-x-2">
          <SimpleDropdown text="Last 7 days" />
          <button
            onClick={() => navigate(-1)}
            className="bg-[#16c66c] hover:bg-[#238e4e] text-white text-sm px-3 py-1.5 rounded-md flex items-center"
          >
            <FaArrowLeft className="mr-1.5" /> Back
          </button>
        </div>
      );
    }
    if (pathname === "/user/notifications") {
      return (
        <div className="flex items-center space-x-2">
          <SimpleDropdown text="Last 7 days" />
          <button className="bg-[#16c66c] hover:bg-[#238e4e] text-white text-sm px-3 py-1.5 rounded-md">
            Finish
          </button>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div
          className="hidden md:flex flex-col border-r border-gray-200 bg-white flex-shrink-0"
          style={{ height: "100vh", width: "300px" }}
        >
          <div className="p-4 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center">
              <img
                src={GuardLogo}
                alt="Guard Logo"
                className="h-16 w-auto object-contain"
              />
            </div>
          </div>

          {/* Fixed Navigation Links - DESKTOP */}
          <div className="flex-shrink-0 p-3">
            <Link
              to="/user/credit"
              className={`flex items-center px-3 py-2.5 rounded-md my-1 ${isActive("/user/credit")}`}
            >
              <FaCreditCard className="w-5 h-5 mr-3 text-gray-500" />
              <span className="text-sm">Credit Cards</span>
            </Link>

            <Link
              to="/user/bank"
              className={`flex items-center px-3 py-2.5 rounded-md my-1 ${isActive("/user/bank")}`}
            >
              <FaUniversity className="w-5 h-5 mr-3 text-gray-500" />
              <span className="text-sm">Bank Accounts</span>
            </Link>

            <Link
              to="/user/affiliate"
              className={`flex items-center px-3 py-2.5 rounded-md my-1 ${isActive("/user/affiliate")}`}
            >
              <FaPercent className="w-5 h-5 mr-3 text-gray-500" />
              <span className="text-sm">Affiliate Offers</span>
            </Link>

            <div className="my-1">
              <button
                onClick={() => setRewardsSubMenuOpen(!rewardsSubMenuOpen)}
                className={`flex items-center justify-between w-full px-3 py-2.5 rounded-md ${isActive("/user/rewards")}`}
              >
                <div className="flex items-center">
                  <FaGift className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="text-sm">Rewards / Benefits</span>
                </div>
                {rewardsSubMenuOpen ? (
                  <FaChevronDown className="w-3 h-3" />
                ) : (
                  <FaChevronRight className="w-3 h-3" />
                )}
              </button>

              {rewardsSubMenuOpen && (
                <div className="ml-6 pl-3 border-l border-gray-200">
                  <Link
                    to="/user/rewards"
                    className={`flex items-center px-3 py-2 rounded-md my-1 text-sm ${isExactActive("/user/rewards")}`}
                  >
                    <svg
                      className="w-4 h-4 mr-2 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    <span>Overview</span>
                  </Link>
                  <Link
                    to="/user/rewards/bonus-tracker"
                    className={`flex items-center px-3 py-2 rounded-md my-1 text-sm ${isExactActive("/user/rewards/bonus-tracker")}`}
                  >
                    <span className="inline-flex items-center justify-center w-5 h-5 mr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-blue-500"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                      </svg>
                    </span>
                    <span>Bonus Tracker</span>
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Scrollable Offers & Insights Section - DESKTOP */}
          <div className="flex-1 overflow-y-auto border-t border-gray-200">
            {/* Affiliate Offers Section */}
            <div className="px-4 py-3">
              <h3 className="text-xs font-medium text-gray-400 uppercase mb-2">
                Affiliate Offers
              </h3>
              <div className="bg-blue-50 rounded-lg p-3 mb-2">
                <h4 className="font-medium text-gray-700 text-sm">
                  Best Card for Dining: Amex Gold
                </h4>
                <p className="text-xs text-gray-500 mb-2">
                  Use this card for restaurants to earn 4x points on dining
                  purchases
                </p>
                <button className="bg-blue-500 text-white text-xs px-3 py-1 rounded-md">
                  Learn More
                </button>
              </div>
              <div className="bg-blue-50 rounded-lg p-3">
                <h4 className="font-medium text-gray-700 text-sm">
                  Chase Sapphire Preferred
                </h4>
                <p className="text-xs text-gray-500 mb-2">
                  Earn 60,000 bonus points after spending $4,000 in first 3
                  months
                </p>
                <button className="bg-blue-500 text-white text-xs px-3 py-1 rounded-md">
                  View Offer
                </button>
              </div>
            </div>

            {/* Spending Insight Section */}
          </div>

          {/* Fixed Bottom Content - DESKTOP */}
          <div className="border-t border-gray-200 bg-white flex-shrink-0">
            <div className="px-4 py-3">
              <h3 className="text-xs font-medium text-gray-400 uppercase mb-2">
                Spending Insight
              </h3>
              <div className="bg-green-50 rounded-lg p-3">
                <h4 className="font-medium text-gray-700 text-sm mb-2">
                  Monthly Overview
                </h4>
                <div className="mb-2">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Dining</span>
                    <span className="text-green-600">30%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: "30%" }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Travel</span>
                    <span className="text-blue-600">15%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: "15%" }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <Link
              to="/user/notifications"
              className="flex items-center px-6 py-3 hover:bg-gray-100"
            >
              <div className="w-5 h-5 mr-3 relative flex items-center justify-center">
                <FaBell className="text-gray-500" />
                <span className="absolute h-1.5 w-1.5 rounded-full bg-red-500 top-0 right-0"></span>
              </div>
              <span className="text-sm text-gray-600">Notifications</span>
            </Link>

            <Link
              to="/user/settings"
              className="flex items-center px-6 py-3 hover:bg-gray-100"
            >
              <FaCog className="w-5 h-5 mr-3 text-gray-500" />
              <span className="text-sm text-gray-600">Account Settings</span>
            </Link>

            <button
              onClick={handleLogout}
              className="flex items-center px-6 py-3 hover:bg-gray-100 w-full text-left"
            >
              <FaSignOutAlt className="w-5 h-5 mr-3 text-gray-500" />
              <span className="text-sm text-gray-600">Logout</span>
            </button>
          </div>
        </div>

        {/* Mobile Header */}
        <div className="md:hidden fixed top-0 left-0 right-0 z-30 bg-white border-b border-gray-200">
          <div className="flex justify-between items-center p-4">
            <button
              onClick={() => setMobileMenuOpen(true)}
              className="text-gray-600"
            >
              <FaBars size={20} />
            </button>
            <div className="flex items-center">
              <img
                src={GuardLogo}
                alt="Guard Logo"
                className="h-12 w-auto object-contain"
              />
            </div>
            <button
              onClick={() => {
                if (location.pathname === "/credit") {
                  setShowQuilttModal(true);
                }
                // Add other conditions here if this button has other functions on other pages
              }}
              className="bg-[#16c66c] text-white px-2.5 py-1 rounded-md text-sm flex items-center"
            >
              <FaPlus />
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="fixed inset-0 z-50 md:hidden">
            <div
              className="fixed inset-0 bg-gray-600 bg-opacity-75"
              onClick={() => setMobileMenuOpen(false)}
            ></div>
            <div className="relative h-full flex flex-col w-80 max-w-[80%] bg-white">
              {/* Fixed Top Content - Mobile */}
              <div className="p-4 flex justify-between items-center border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center">
                  <img
                    src={GuardLogo}
                    alt="Guard Logo"
                    className="h-12 w-auto object-contain"
                  />
                </div>
                <button
                  onClick={() => setMobileMenuOpen(false)}
                  className="text-gray-500"
                >
                  <FaTimes size={20} />
                </button>
              </div>

              {/* Fixed Navigation Links - MOBILE */}
              <div className="flex-shrink-0 p-3">
                <Link
                  to="/user/credit"
                  className={`flex items-center px-3 py-2.5 rounded-md my-1 ${isActive("/credit")}`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <FaCreditCard className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="text-sm">Credit Cards</span>
                </Link>

                <Link
                  to="/user/bank"
                  className={`flex items-center px-3 py-2.5 rounded-md my-1 ${isActive("/bank")}`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <FaUniversity className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="text-sm">Bank Accounts</span>
                </Link>

                <Link
                  to="/user/affiliate"
                  className={`flex items-center px-3 py-2.5 rounded-md my-1 ${isActive("/affiliate")}`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <FaPercent className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="text-sm">Affiliate Offers</span>
                </Link>

                <div className="my-1">
                  <button
                    onClick={() => setRewardsSubMenuOpen(!rewardsSubMenuOpen)}
                    className={`flex items-center justify-between w-full px-3 py-2.5 rounded-md ${isActive("/user/rewards")}`}
                  >
                    <div className="flex items-center">
                      <FaGift className="w-5 h-5 mr-3 text-gray-500" />
                      <span className="text-sm">Rewards / Benefits</span>
                    </div>
                    {rewardsSubMenuOpen ? (
                      <FaChevronDown className="w-3 h-3" />
                    ) : (
                      <FaChevronRight className="w-3 h-3" />
                    )}
                  </button>

                  {rewardsSubMenuOpen && (
                    <div className="ml-6 pl-3 border-l border-gray-200">
                      <Link
                        to="/user/rewards"
                        className={`flex items-center px-3 py-2 rounded-md my-1 text-sm ${isExactActive("/user/rewards")}`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <svg
                          className="w-4 h-4 mr-2 text-blue-500"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                          />
                        </svg>
                        <span>Overview</span>
                      </Link>
                      <Link
                        to="/user/rewards/bonus-tracker"
                        className={`flex items-center px-3 py-2 rounded-md my-1 text-sm ${isExactActive("/user/rewards/bonus-tracker")}`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <span className="inline-flex items-center justify-center w-5 h-5 mr-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-blue-500"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                          </svg>
                        </span>
                        <span>Bonus Tracker</span>
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* Scrollable Offers & Insights Section - MOBILE */}
              <div className="flex-1 overflow-y-auto border-t border-gray-200">
                {/* Affiliate Offers Section - Mobile */}
                <div className="px-4 py-3">
                  <h3 className="text-xs font-medium text-gray-400 uppercase mb-2">
                    Affiliate Offers
                  </h3>
                  <div className="bg-blue-50 rounded-lg p-3 mb-2">
                    <h4 className="font-medium text-gray-700 text-sm">
                      Best Card for Dining: Amex Gold
                    </h4>
                    <p className="text-xs text-gray-500 mb-2">
                      Use this card for restaurants to earn 4x points on dining
                      purchases
                    </p>
                    <button className="bg-blue-500 text-white text-xs px-3 py-1 rounded-md">
                      Learn More
                    </button>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <h4 className="font-medium text-gray-700 text-sm">
                      Chase Sapphire Preferred
                    </h4>
                    <p className="text-xs text-gray-500 mb-2">
                      Earn 60,000 bonus points after spending $4,000 in first 3
                      months
                    </p>
                    <button className="bg-blue-500 text-white text-xs px-3 py-1 rounded-md">
                      View Offer
                    </button>
                  </div>
                </div>

                {/* Spending Insight Section - Mobile */}
                <div className="px-4 py-3">
                  <h3 className="text-xs font-medium text-gray-400 uppercase mb-2">
                    Spending Insight
                  </h3>
                  <div className="bg-green-50 rounded-lg p-3">
                    <h4 className="font-medium text-gray-700 text-sm mb-2">
                      Monthly Overview
                    </h4>
                    <div className="mb-2">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Dining</span>
                        <span className="text-green-600">30%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: "30%" }}
                        ></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Travel</span>
                        <span className="text-blue-600">15%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: "15%" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fixed Bottom Content - MOBILE */}
              <div className="border-t border-gray-200 p-0 flex-shrink-0">
                <Link
                  to="/user/notifications"
                  className="flex items-center px-6 py-3 hover:bg-gray-100"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <div className="w-5 h-5 mr-3 relative flex items-center justify-center">
                    <FaBell className="text-gray-500" />
                    <span className="absolute h-1.5 w-1.5 rounded-full bg-red-500 top-0 right-0"></span>
                  </div>
                  <span className="text-sm text-gray-600">Notifications</span>
                </Link>

                <Link
                  to="/user/settings"
                  className="flex items-center px-6 py-3 hover:bg-gray-100"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <FaCog className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    Account Settings
                  </span>
                </Link>

                <button
                  onClick={handleLogout}
                  className="flex items-center px-6 py-3 hover:bg-gray-100 w-full text-left"
                >
                  <FaSignOutAlt className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="text-sm text-gray-600">Logout</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Main content wrapper */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top user bar in main content */}
          <div className="flex justify-between items-center py-4 px-6 bg-white border-b border-gray-200">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full flex items-center justify-center text-yellow-800 font-medium text-sm mr-3">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 40 40"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 0C31.0457 0 40 8.95431 40 20C40 31.0457 31.0457 40 20 40C8.95431 40 0 31.0457 0 20C0 8.95431 8.95431 0 20 0Z"
                    fill="#FEF3C7"
                  />
                  <path
                    d="M20 0C31.0457 0 40 8.95431 40 20C40 31.0457 31.0457 40 20 40C8.95431 40 0 31.0457 0 20C0 8.95431 8.95431 0 20 0Z"
                    stroke="#E5E7EB"
                  />
                  <path d="M27 28H13V12H27V28Z" stroke="#E5E7EB" />
                  <g clip-path="url(#clip0_177_9804)">
                    <path
                      d="M20 20C21.0609 20 22.0783 19.5786 22.8284 18.8284C23.5786 18.0783 24 17.0609 24 16C24 14.9391 23.5786 13.9217 22.8284 13.1716C22.0783 12.4214 21.0609 12 20 12C18.9391 12 17.9217 12.4214 17.1716 13.1716C16.4214 13.9217 16 14.9391 16 16C16 17.0609 16.4214 18.0783 17.1716 18.8284C17.9217 19.5786 18.9391 20 20 20ZM18.5719 21.5C15.4938 21.5 13 23.9937 13 27.0719C13 27.5844 13.4156 28 13.9281 28H26.0719C26.5844 28 27 27.5844 27 27.0719C27 23.9937 24.5063 21.5 21.4281 21.5H18.5719Z"
                      fill="#F59E0B"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_177_9804">
                      <path d="M13 12H27V28H13V12Z" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div>
                <div className="text-gray-700 font-medium">Howdy,</div>
                <div className="text-gray-700">{userFullName || "User"}</div>
              </div>
            </div>
            <div>{renderDynamicTopBarActions()}</div>
          </div>

          {/* Page content */}
          <div className="flex-1 overflow-auto p-6 md:py-6 md:px-6 pt-6">
            {children}
          </div>
        </div>
      </div>
      {/* Conditionally render QuilttContainer */}
      {showQuilttModal && (
        <QuilttContainer
          onSuccess={() => setShowQuilttModal(false)}
          onExit={() => setShowQuilttModal(false)}
          onError={(error) => {
            console.error("Quiltt Error:", error);
            setShowQuilttModal(false);
          }}
        />
      )}
    </div>
  );
};

export default UserLayout;
