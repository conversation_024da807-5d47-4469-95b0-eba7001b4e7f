import React from "react";

const TrashIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.3199 4.16666C6.90053 2.7025 8.32902 1.66666 10.0007 1.66666C11.6725 1.66666 13.1009 2.7025 13.6816 4.16666H17.7083C18.0535 4.16666 18.3333 4.44649 18.3333 4.79166C18.3333 5.13684 18.0535 5.41666 17.7083 5.41666H16.6276L15.8823 16.9689C15.8328 17.7363 15.196 18.3333 14.427 18.3333H5.57293C4.80396 18.3333 4.16713 17.7363 4.11762 16.9689L3.37232 5.41666H2.29163C1.94645 5.41666 1.66663 5.13684 1.66663 4.79166C1.66663 4.44649 1.94645 4.16666 2.29163 4.16666H6.3199ZM7.71797 4.16666C8.19942 3.41479 9.04236 2.91666 10.0007 2.91666C10.9591 2.91666 11.8021 3.41479 12.2835 4.16666H7.71797ZM8.74996 8.95833C8.74996 8.61315 8.47014 8.33333 8.12496 8.33333C7.77978 8.33333 7.49996 8.61315 7.49996 8.95833V13.5417C7.49996 13.8868 7.77978 14.1667 8.12496 14.1667C8.47014 14.1667 8.74996 13.8868 8.74996 13.5417V8.95833ZM11.875 8.33333C12.2201 8.33333 12.5 8.61315 12.5 8.95833V13.5417C12.5 13.8868 12.2201 14.1667 11.875 14.1667C11.5298 14.1667 11.25 13.8868 11.25 13.5417V8.95833C11.25 8.61315 11.5298 8.33333 11.875 8.33333Z"
        fill={fill}
      />
    </svg>
  );
};

export default TrashIcon;
