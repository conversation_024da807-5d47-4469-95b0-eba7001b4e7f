import React from 'react';

const FileLineIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
    return (
        <svg onClick={onClick} className={`${className}`} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path fillRule="evenodd" clipRule="evenodd" d="M9.99998 1.66675H4.79165C3.98623 1.66675 3.33331 2.31967 3.33331 3.12508V16.8751C3.33331 17.6805 3.98623 18.3334 4.79165 18.3334H15.2083C16.0137 18.3334 16.6666 17.6805 16.6666 16.8751V8.33342H11.4583C10.6529 8.33342 9.99998 7.6805 9.99998 6.87508V1.66675ZM6.66665 11.8751C6.66665 11.5299 6.94647 11.2501 7.29165 11.2501H10.2083C10.5535 11.2501 10.8333 11.5299 10.8333 11.8751C10.8333 12.2203 10.5535 12.5001 10.2083 12.5001H7.29165C6.94647 12.5001 6.66665 12.2203 6.66665 11.8751ZM7.29165 14.5834C6.94647 14.5834 6.66665 14.8632 6.66665 15.2084C6.66665 15.5536 6.94647 15.8334 7.29165 15.8334H12.7083C13.0535 15.8334 13.3333 15.5536 13.3333 15.2084C13.3333 14.8632 13.0535 14.5834 12.7083 14.5834H7.29165Z" fill={fill} />
            <path d="M16.2971 7.08342C16.2786 7.06258 16.2593 7.04223 16.2395 7.0224L11.311 2.09388C11.2912 2.07405 11.2708 2.05484 11.25 2.03628V6.87508C11.25 6.99014 11.3433 7.08342 11.4583 7.08342H16.2971Z" fill={fill} />
        </svg>
    );
};

export default FileLineIcon;