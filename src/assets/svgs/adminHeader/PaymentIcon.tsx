const PaymentIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.66669 9.99999C1.66669 5.39762 5.39765 1.66666 10 1.66666C14.6024 1.66666 18.3334 5.39762 18.3334 9.99999C18.3334 14.6024 14.6024 18.3333 10 18.3333C5.39765 18.3333 1.66669 14.6024 1.66669 9.99999ZM10.8334 4.99918C10.8334 4.53894 10.4603 4.16584 10 4.16584C9.53979 4.16584 9.16669 4.53894 9.16669 4.99918V5.49714C8.73902 5.58509 8.33396 5.75096 7.98977 6.02232C7.39607 6.49039 7.09429 7.18495 7.09429 8.00072C7.09429 8.95422 7.48667 9.61819 8.08826 10.0613C8.55841 10.4075 9.1512 10.6053 9.59515 10.7535C9.64275 10.7693 9.68864 10.7847 9.73248 10.7995L9.81787 10.8284C10.334 11.0031 10.6755 11.1187 10.9234 11.3013C11.1038 11.4341 11.2391 11.6026 11.2391 12.0199C11.2391 12.4006 11.1155 12.5814 10.9784 12.6895C10.8106 12.8217 10.5003 12.9371 10 12.9371C9.40157 12.9371 9.0447 12.6688 8.86991 12.355C8.64593 11.953 8.13842 11.8086 7.73637 12.0326C7.33431 12.2566 7.18995 12.7641 7.41393 13.1661C7.77696 13.8178 8.39036 14.2996 9.16669 14.5005V15C9.16669 15.4602 9.53979 15.8333 10 15.8333C10.4603 15.8333 10.8334 15.4602 10.8334 15V14.5235C11.261 14.4355 11.6661 14.2696 12.0103 13.9983C12.604 13.5302 12.9058 12.8356 12.9058 12.0199C12.9058 11.0664 12.5134 10.4024 11.9118 9.95934C11.4416 9.61307 10.8488 9.41527 10.4049 9.26713C10.3573 9.25125 10.3114 9.23594 10.2676 9.22108L10.1822 9.19216C9.66606 9.01748 9.3246 8.90191 9.07662 8.71928C8.89627 8.58645 8.76096 8.41801 8.76096 8.00072C8.76096 7.61995 8.88456 7.43922 9.02165 7.33114C9.18941 7.19887 9.4997 7.08347 10 7.08347C10.5985 7.08347 10.9553 7.35182 11.1301 7.66557C11.3541 8.06763 11.8616 8.21199 12.2637 7.988C12.6657 7.76402 12.8101 7.25651 12.5861 6.85446C12.2231 6.2028 11.6097 5.72103 10.8334 5.52006V4.99918Z"
        fill="#A8A8A8"
      />
    </svg>
  );
};

export default PaymentIcon;
