# Integration Tests

This directory will contain integration tests for testing interactions between components.

Integration tests focus on testing how multiple units work together, such as how a form component interacts with a validation utility.

## Structure

Tests should be organized by feature or workflow:

```
integration/
  auth/
    login.spec.tsx
  wireframe/
    editor.spec.tsx
```

## To be implemented in the future
