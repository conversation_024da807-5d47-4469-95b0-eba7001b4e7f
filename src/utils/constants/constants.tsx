export const EditWireframeTabTypes = {
  Requirements: "Requirements",
  // Model: "Model",
  // APIV2: "APIV2",
  API: "API",
  // "Web/React": "Web/React",
  // IOS: "IOS",
  // Android: "Android",
  Deployment: "Deployment",
};
export const APIRouteConfigTabs = {
  General: "General",
  Authentication: "Authentication",
  Api_Input: "Api_Input",
};
export const WorkflowTypes = {
  APIRoute: "API_Route",
  IfControl: "if_control",
  SwitchControl: "switch_control",
  Variables: "variables",
  DBQuery: "db_query",
  CustomCode: "custom_code",
  HttpRequest: "http_request",
  SuccessResponse: "success_response",
  ErrorResponse: "error_response",
};
export const DragSegmentsTypes = {
  TRUE: "true",
  FALSE: "false",
  NEXT: "next",
};
export const HttpMethods = {
  GET: "GET",
  POST: "POST",
  PUT: "PUT",
  PATCH: "PATCH",
  DELETE: "DELETE",
};
export const operators = ["===", "!==", ">", ">=", "<", "<="];
export const operatorMap = {
  "===": "equal",
  "!==": "not equal",
  ">": "greater than",
  ">=": "greater than or equal to",
  "<": "less than",
  "<=": "less than or equal to",
};
export const simpleOperatorMap = {
  "===": "=",
  "!==": "!=",
  ">": ">",
  ">=": ">=",
  "<": "<",
  "<=": "<=",
};
