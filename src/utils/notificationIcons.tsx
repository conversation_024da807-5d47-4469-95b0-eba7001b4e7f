import React from "react";
import {
  FaBell,
  FaCheckCircle,
  FaExclamationTriangle,
  FaGift,
  FaPercent,
  FaReceipt,
  FaRegCreditCard,
  FaSearchDollar,
  FaCreditCard,
  FaUniversity,
  FaShieldAlt,
} from "react-icons/fa";

type NotificationType =
  | "payment_due"
  | "payment_success"
  | "reward"
  | "offer"
  | "card_linked"
  | "card_unlinked"
  | "bank_linked"
  | "security"
  | "apr_offer"
  | "apr_expiring"
  | "bonus_achieved"
  | "bonus_progress"
  | "default";

interface NotificationIconConfig {
  icon: React.ReactNode;
  bgColor: string;
  iconColor: string;
}

const iconMap: Record<NotificationType, NotificationIconConfig> = {
  payment_due: {
    icon: <FaReceipt />,
    bgColor: "bg-red-100",
    iconColor: "text-red-500",
  },
  payment_success: {
    icon: <FaCheckCircle />,
    bgColor: "bg-green-100",
    iconColor: "text-green-500",
  },
  reward: {
    icon: <FaGift />,
    bgColor: "bg-green-100",
    iconColor: "text-green-500",
  },
  offer: {
    icon: <FaPercent />,
    bgColor: "bg-purple-100",
    iconColor: "text-purple-500",
  },
  card_linked: {
    icon: <FaRegCreditCard />,
    bgColor: "bg-blue-100",
    iconColor: "text-blue-500",
  },
  card_unlinked: {
    icon: <FaCreditCard />,
    bgColor: "bg-yellow-100",
    iconColor: "text-yellow-600",
  },
  bank_linked: {
    icon: <FaUniversity />,
    bgColor: "bg-blue-100",
    iconColor: "text-blue-500",
  },
  security: {
    icon: <FaShieldAlt />,
    bgColor: "bg-red-100",
    iconColor: "text-red-500",
  },
  apr_offer: {
    icon: <FaPercent />,
    bgColor: "bg-purple-100",
    iconColor: "text-purple-500",
  },
  apr_expiring: {
    icon: <FaExclamationTriangle />,
    bgColor: "bg-yellow-100",
    iconColor: "text-yellow-500",
  },
  bonus_achieved: {
    icon: <FaGift />,
    bgColor: "bg-green-100",
    iconColor: "text-green-500",
  },
  bonus_progress: {
    icon: <FaSearchDollar />,
    bgColor: "bg-blue-100",
    iconColor: "text-blue-500",
  },
  default: {
    icon: <FaBell />,
    bgColor: "bg-gray-100",
    iconColor: "text-gray-500",
  },
};

export const getNotificationIcon = (type: string): NotificationIconConfig => {
  return iconMap[type as NotificationType] || iconMap.default;
};
