import axios from "axios";

interface QuilttTokenResponse {
  token: string;
  expires_at?: string;
}

interface QuilttBalance {
  id: string;
  at: string;
  available: number;
  current: number;
  limit?: number;
}

interface QuilttInstitution {
  name: string;
}

interface QuilttAccount {
  id: string;
  name: string;
  type: string;
  verified: boolean;
  metadata: Record<string, any>;
  institution: QuilttInstitution;
  balance: QuilttBalance;
}

interface GetAccountsResponse {
  data: {
    accounts: QuilttAccount[];
  };
}

class QuilttApi {
  private baseUrl = "https://api.quiltt.io/v1";
  private token: string | null = null;
  private tokenExpiresAt: Date | null = null;

  /**
   * Generate a Quiltt token from the backend
   */
  async generateToken(auth_token: string): Promise<string> {
    const baseUrl = "https://mkdlabs.com";
    const url = `${baseUrl}/v3/api/quiltt/token`;

    try {
      const response = await axios.get<QuilttTokenResponse>(url, {
        headers: {
          Authorization: `Bearer ${auth_token}`,
        },
      });
      this.token = response.data.token;

      if (response.data.expires_at) {
        this.tokenExpiresAt = new Date(response.data.expires_at);
      } else {
        // If no expiration is provided, set it to expire in 1 hour
        this.tokenExpiresAt = new Date(Date.now() + 60 * 60 * 1000);
      }

      return this.token;
    } catch (error) {
      console.error("Error generating Quiltt token:", error);
      throw new Error("Failed to generate Quiltt token");
    }
  }

  /**
   * Get a valid token, generating a new one if needed
   */
  async getToken(auth_token: string): Promise<string> {
    const now = new Date();

    // If token doesn't exist or is about to expire (within 5 minutes), generate a new one
    if (
      !this.token ||
      !this.tokenExpiresAt ||
      this.tokenExpiresAt.getTime() - now.getTime() < 5 * 60 * 1000
    ) {
      return this.generateToken(auth_token);
    }

    return this.token;
  }

  /**
   * Execute a GraphQL query against the Quiltt API
   */
  async executeGraphQL<T>(query: string, variables = {}): Promise<T> {
    try {
      // Ensure we have a valid token before proceeding
      if (!this.token) {
        throw new Error("Authentication token required for GraphQL queries");
      }

      const response = await axios.post(
        `${this.baseUrl}/graphql`,
        {
          query,
          variables,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.token}`,
          },
        }
      );

      if (response.data.errors) {
        throw new Error(response.data.errors[0].message);
      }

      return response.data;
    } catch (error) {
      console.error("Error executing GraphQL query:", error);
      throw error;
    }
  }

  /**
   * Fetch connected bank accounts
   */
  async getAccounts(): Promise<QuilttAccount[]> {
    const query = `
      query GetAccounts {
        accounts {
          id
          name
          type
          verified
          metadata
          institution {
            name
          }
          balance {
            id
            at
            available
            current
            limit
          }
        }
      }
    `;

    try {
      const response = await this.executeGraphQL<GetAccountsResponse>(query);
      return response.data.accounts;
    } catch (error) {
      console.error("Error fetching accounts:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const quilttApi = new QuilttApi();
