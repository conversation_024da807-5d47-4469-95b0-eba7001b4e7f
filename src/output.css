*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: "Core Sans CR", sans-serif;
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

input[type="range"]::-webkit-slider-thumb {
  height: 1rem;
  width: 1rem;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

.dark input[type="range"]::-webkit-slider-thumb {
}

input[type="range"]:disabled::-webkit-slider-thumb {
  background: #a3a3a3;
}

input[type="range"]:disabled:focus::-webkit-slider-thumb {
  background: #a3a3a3;
}

input[type="range"]:disabled:active::-webkit-slider-thumb {
  background: #a3a3a3;
}

.dark input[type="range"]:disabled::-webkit-slider-thumb {
  background: #737373;
}

.dark input[type="range"]:disabled:focus::-webkit-slider-thumb {
  background: #737373;
}

.dark input[type="range"]:disabled:active::-webkit-slider-thumb {
  background: #737373;
}

input[type="range"]::-moz-range-thumb {
  height: 1rem;
  width: 1rem;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

.dark input[type="range"]::-moz-range-thumb {
}

input[type="range"]:disabled::-moz-range-thumb {
  background: #a3a3a3;
}

.dark input[type="range"]:disabled::-moz-range-thumb {
  background: #737373;
}

input[type="range"]::-moz-range-progress {
}

input[type="range"]::-ms-fill-lower {
}

.dark input[type="range"]::-moz-range-progress {
}

.dark input[type="range"]::-ms-fill-lower {
}

input[type="range"]:focus {
  outline: none;
}

input[type="range"]:focus::-webkit-slider-thumb {
}

input[type="range"]:active::-webkit-slider-thumb {
}

.dark input[type="range"]:focus::-webkit-slider-thumb {
}

.dark input[type="range"]:active::-webkit-slider-thumb {
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

.container {
  width: 100%;
}

@media (min-width: 320px) {
  .container {
    max-width: 320px;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.form-checkbox,.form-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

.form-radio {
  border-radius: 100%;
}

.form-checkbox:focus,.form-radio:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.form-checkbox:checked,.form-radio:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-radio:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  .form-radio:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

.form-checkbox:checked:hover,.form-checkbox:checked:focus,.form-radio:checked:hover,.form-radio:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.\!fixed {
  position: fixed !important;
}

.fixed {
  position: fixed;
}

.\!absolute {
  position: absolute !important;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.\!right-\[3\.25rem\] {
  right: 3.25rem !important;
}

.-bottom-1 {
  bottom: -0.25rem;
}

.-bottom-\[35\%\] {
  bottom: -35%;
}

.-bottom-\[47px\] {
  bottom: -47px;
}

.-left-\[0\.05rem\] {
  left: -0.05rem;
}

.-left-\[15px\] {
  left: -15px;
}

.-left-\[25\%\] {
  left: -25%;
}

.-left-\[30\%\] {
  left: -30%;
}

.-left-\[9999px\] {
  left: -9999px;
}

.-right-1 {
  right: -0.25rem;
}

.-right-3 {
  right: -0.75rem;
}

.-right-\[0\.05rem\] {
  right: -0.05rem;
}

.-right-\[170px\] {
  right: -170px;
}

.-right-full {
  right: -100%;
}

.-top-1 {
  top: -0.25rem;
}

.-top-\[0\.05rem\] {
  top: -0.05rem;
}

.-top-\[18px\] {
  top: -18px;
}

.-top-\[21px\] {
  top: -21px;
}

.-top-\[35px\] {
  top: -35px;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-0\.5 {
  bottom: 0.125rem;
}

.bottom-1\/2 {
  bottom: 50%;
}

.bottom-5 {
  bottom: 1.25rem;
}

.bottom-8 {
  bottom: 2rem;
}

.bottom-\[6\.5625rem\] {
  bottom: 6.5625rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-10 {
  left: 2.5rem;
}

.left-2 {
  left: 0.5rem;
}

.left-3 {
  left: 0.75rem;
}

.left-4 {
  left: 1rem;
}

.left-\[1px\] {
  left: 1px;
}

.left-\[50\%\] {
  left: 50%;
}

.left-\[50px\] {
  left: 50px;
}

.left-\[calc\(50\%-1px\)\] {
  left: calc(50% - 1px);
}

.right-0 {
  right: 0px;
}

.right-0\.5 {
  right: 0.125rem;
}

.right-1 {
  right: 0.25rem;
}

.right-1\.5 {
  right: 0.375rem;
}

.right-2 {
  right: 0.5rem;
}

.right-3 {
  right: 0.75rem;
}

.right-4 {
  right: 1rem;
}

.right-5 {
  right: 1.25rem;
}

.right-9 {
  right: 2.25rem;
}

.top-0 {
  top: 0px;
}

.top-1 {
  top: 0.25rem;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 2.5rem;
}

.top-12 {
  top: 3rem;
}

.top-2 {
  top: 0.5rem;
}

.top-3 {
  top: 0.75rem;
}

.top-4 {
  top: 1rem;
}

.top-5 {
  top: 1.25rem;
}

.top-\[-15px\] {
  top: -15px;
}

.top-\[-2000\%\] {
  top: -2000%;
}

.top-\[11px\] {
  top: 11px;
}

.top-\[13px\] {
  top: 13px;
}

.top-\[1px\] {
  top: 1px;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[50px\] {
  top: 50px;
}

.top-\[85\%\] {
  top: 85%;
}

.top-\[90\%\] {
  top: 90%;
}

.top-full {
  top: 100%;
}

.\!z-10 {
  z-index: 10 !important;
}

.\!z-40 {
  z-index: 40 !important;
}

.-z-10 {
  z-index: -10;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[100000000001\] {
  z-index: 100000000001;
}

.z-\[1035\] {
  z-index: 1035;
}

.z-\[1040\] {
  z-index: 1040;
}

.z-\[1065\] {
  z-index: 1065;
}

.z-\[1066\] {
  z-index: 1066;
}

.z-\[1070\] {
  z-index: 1070;
}

.z-\[1080\] {
  z-index: 1080;
}

.z-\[1100\] {
  z-index: 1100;
}

.z-\[19\] {
  z-index: 19;
}

.z-\[2\] {
  z-index: 2;
}

.z-\[5\] {
  z-index: 5;
}

.z-\[999999991\] {
  z-index: 999999991;
}

.z-\[999999992\] {
  z-index: 999999992;
}

.z-\[9999999999\] {
  z-index: 9999999999;
}

.z-\[999999999\] {
  z-index: 999999999;
}

.z-\[99999999\] {
  z-index: 99999999;
}

.z-\[9999999\] {
  z-index: 9999999;
}

.z-\[999999\] {
  z-index: 999999;
}

.z-\[99999\] {
  z-index: 99999;
}

.z-\[9999\] {
  z-index: 9999;
}

.z-\[999\] {
  z-index: 999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.\!-m-px {
  margin: -1px !important;
}

.\!m-auto {
  margin: auto !important;
}

.-m-px {
  margin: -1px;
}

.m-0 {
  margin: 0px;
}

.m-1 {
  margin: 0.25rem;
}

.m-5 {
  margin: 1.25rem;
}

.m-\[-1px\] {
  margin: -1px;
}

.m-auto {
  margin: auto;
}

.\!my-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-\[10px\] {
  margin-left: 10px;
  margin-right: 10px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-\[5px\] {
  margin-top: 5px;
  margin-bottom: 5px;
}

.my-\[6rem\] {
  margin-top: 6rem;
  margin-bottom: 6rem;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.\!mb-10 {
  margin-bottom: 2.5rem !important;
}

.\!mt-0 {
  margin-top: 0px !important;
}

.-mb-px {
  margin-bottom: -1px;
}

.-ml-0\.5 {
  margin-left: -0.125rem;
}

.-ml-\[1\.5rem\] {
  margin-left: -1.5rem;
}

.-ml-px {
  margin-left: -1px;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mr-12 {
  margin-right: -3rem;
}

.-mr-3 {
  margin-right: -0.75rem;
}

.-mr-px {
  margin-right: -1px;
}

.-mt-0\.5 {
  margin-top: -0.125rem;
}

.-mt-1 {
  margin-top: -0.25rem;
}

.-mt-3 {
  margin-top: -0.75rem;
}

.-mt-px {
  margin-top: -1px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-0\.5 {
  margin-bottom: 0.125rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-1\.5 {
  margin-bottom: 0.375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-28 {
  margin-bottom: 7rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-\[0\.125rem\] {
  margin-bottom: 0.125rem;
}

.mb-\[10px\] {
  margin-bottom: 10px;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-1\.5 {
  margin-left: 0.375rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-\[30px\] {
  margin-left: 30px;
}

.ml-\[3px\] {
  margin-left: 3px;
}

.ml-auto {
  margin-left: auto;
}

.mr-0 {
  margin-right: 0px;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-6 {
  margin-right: 1.5rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-\[-8px\] {
  margin-right: -8px;
}

.mr-\[20\%\] {
  margin-right: 20%;
}

.mr-\[6px\] {
  margin-right: 6px;
}

.mr-\[8px\] {
  margin-right: 8px;
}

.mr-auto {
  margin-right: auto;
}

.mt-0 {
  margin-top: 0px;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-2\.5 {
  margin-top: 0.625rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[0\.15rem\] {
  margin-top: 0.15rem;
}

.mt-auto {
  margin-top: auto;
}

.box-border {
  box-sizing: border-box;
}

.box-content {
  box-sizing: content-box;
}

.\!block {
  display: block !important;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.\!flex {
  display: flex !important;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.\!table {
  display: table !important;
}

.table {
  display: table;
}

.\!grid {
  display: grid !important;
}

.grid {
  display: grid;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.\!h-0 {
  height: 0px !important;
}

.\!h-4 {
  height: 1rem !important;
}

.\!h-6 {
  height: 1.5rem !important;
}

.\!h-\[12\.25rem\] {
  height: 12.25rem !important;
}

.\!h-\[2\.25rem\] {
  height: 2.25rem !important;
}

.\!h-\[2\.5rem\] {
  height: 2.5rem !important;
}

.\!h-\[2\.65rem\] {
  height: 2.65rem !important;
}

.\!h-\[2rem\] {
  height: 2rem !important;
}

.\!h-\[3\.25rem\] {
  height: 3.25rem !important;
}

.\!h-\[3rem\] {
  height: 3rem !important;
}

.\!h-\[4rem\] {
  height: 4rem !important;
}

.\!h-\[auto\] {
  height: auto !important;
}

.\!h-fit {
  height: -moz-fit-content !important;
  height: fit-content !important;
}

.\!h-full {
  height: 100% !important;
}

.\!h-px {
  height: 1px !important;
}

.h-0 {
  height: 0px;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-2\.5 {
  height: 0.625rem;
}

.h-2\/5 {
  height: 40%;
}

.h-24 {
  height: 6rem;
}

.h-28 {
  height: 7rem;
}

.h-3 {
  height: 0.75rem;
}

.h-32 {
  height: 8rem;
}

.h-36 {
  height: 9rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[\.5919rem\] {
  height: .5919rem;
}

.h-\[\.875rem\] {
  height: .875rem;
}

.h-\[\.9375rem\] {
  height: .9375rem;
}

.h-\[0\.9375rem\] {
  height: 0.9375rem;
}

.h-\[1\.0313rem\] {
  height: 1.0313rem;
}

.h-\[1\.125rem\] {
  height: 1.125rem;
}

.h-\[1\.25rem\] {
  height: 1.25rem;
}

.h-\[1\.4rem\] {
  height: 1.4rem;
}

.h-\[1\.5rem\] {
  height: 1.5rem;
}

.h-\[1\.875rem\] {
  height: 1.875rem;
}

.h-\[100px\] {
  height: 100px;
}

.h-\[100vh\] {
  height: 100vh;
}

.h-\[10px\] {
  height: 10px;
}

.h-\[120px\] {
  height: 120px;
}

.h-\[12rem\] {
  height: 12rem;
}

.h-\[14px\] {
  height: 14px;
}

.h-\[160px\] {
  height: 160px;
}

.h-\[18\.75rem\] {
  height: 18.75rem;
}

.h-\[18px\] {
  height: 18px;
}

.h-\[1rem\] {
  height: 1rem;
}

.h-\[2\.125rem\] {
  height: 2.125rem;
}

.h-\[2\.25rem\] {
  height: 2.25rem;
}

.h-\[2\.5rem\] {
  height: 2.5rem;
}

.h-\[2\.75rem\] {
  height: 2.75rem;
}

.h-\[2\.8rem\] {
  height: 2.8rem;
}

.h-\[260px\] {
  height: 260px;
}

.h-\[270px\] {
  height: 270px;
}

.h-\[2px\] {
  height: 2px;
}

.h-\[2rem\] {
  height: 2rem;
}

.h-\[3\.125rem\] {
  height: 3.125rem;
}

.h-\[3\.75rem\] {
  height: 3.75rem;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[30px\] {
  height: 30px;
}

.h-\[31\.25rem\] {
  height: 31.25rem;
}

.h-\[32px\] {
  height: 32px;
}

.h-\[360px\] {
  height: 360px;
}

.h-\[380px\] {
  height: 380px;
}

.h-\[3rem\] {
  height: 3rem;
}

.h-\[4\.125rem\] {
  height: 4.125rem;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[42px\] {
  height: 42px;
}

.h-\[48px\] {
  height: 48px;
}

.h-\[4px\] {
  height: 4px;
}

.h-\[50\%\] {
  height: 50%;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[50vh\] {
  height: 50vh;
}

.h-\[512px\] {
  height: 512px;
}

.h-\[55px\] {
  height: 55px;
}

.h-\[56px\] {
  height: 56px;
}

.h-\[5rem\] {
  height: 5rem;
}

.h-\[6px\] {
  height: 6px;
}

.h-\[70vh\] {
  height: 70vh;
}

.h-\[72px\] {
  height: 72px;
}

.h-\[90\%\] {
  height: 90%;
}

.h-\[calc\(100\%-100px\)\] {
  height: calc(100% - 100px);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.h-svh {
  height: 100svh;
}

.\!max-h-\[2\.65rem\] {
  max-height: 2.65rem !important;
}

.\!max-h-\[3\.25rem\] {
  max-height: 3.25rem !important;
}

.\!max-h-\[3rem\] {
  max-height: 3rem !important;
}

.\!max-h-\[4rem\] {
  max-height: 4rem !important;
}

.\!max-h-fit {
  max-height: -moz-fit-content !important;
  max-height: fit-content !important;
}

.\!max-h-full {
  max-height: 100% !important;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-14 {
  max-height: 3.5rem;
}

.max-h-48 {
  max-height: 12rem;
}

.max-h-5 {
  max-height: 1.25rem;
}

.max-h-56 {
  max-height: 14rem;
}

.max-h-\[1\.5rem\] {
  max-height: 1.5rem;
}

.max-h-\[1\.875rem\] {
  max-height: 1.875rem;
}

.max-h-\[18\.75rem\] {
  max-height: 18.75rem;
}

.max-h-\[2\.5rem\] {
  max-height: 2.5rem;
}

.max-h-\[2rem\] {
  max-height: 2rem;
}

.max-h-\[3\.125rem\] {
  max-height: 3.125rem;
}

.max-h-\[31\.25rem\] {
  max-height: 31.25rem;
}

.max-h-\[3rem\] {
  max-height: 3rem;
}

.max-h-\[4\.5rem\] {
  max-height: 4.5rem;
}

.max-h-\[50\%\] {
  max-height: 50%;
}

.max-h-\[60px\] {
  max-height: 60px;
}

.max-h-\[70\%\] {
  max-height: 70%;
}

.max-h-\[70vh\] {
  max-height: 70vh;
}

.max-h-\[80vh\] {
  max-height: 80vh;
}

.max-h-\[90\%\] {
  max-height: 90%;
}

.max-h-\[90px\] {
  max-height: 90px;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.max-h-\[95vh\] {
  max-height: 95vh;
}

.max-h-\[calc\(100\%-64px\)\] {
  max-height: calc(100% - 64px);
}

.max-h-fit {
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.max-h-full {
  max-height: 100%;
}

.max-h-screen {
  max-height: 100vh;
}

.max-h-svh {
  max-height: 100svh;
}

.\!min-h-\[2\.65rem\] {
  min-height: 2.65rem !important;
}

.\!min-h-\[3rem\] {
  min-height: 3rem !important;
}

.\!min-h-\[4rem\] {
  min-height: 4rem !important;
}

.\!min-h-\[auto\] {
  min-height: auto !important;
}

.\!min-h-fit {
  min-height: -moz-fit-content !important;
  min-height: fit-content !important;
}

.\!min-h-full {
  min-height: 100% !important;
}

.min-h-0 {
  min-height: 0px;
}

.min-h-14 {
  min-height: 3.5rem;
}

.min-h-20 {
  min-height: 5rem;
}

.min-h-5 {
  min-height: 1.25rem;
}

.min-h-\[1\.5rem\] {
  min-height: 1.5rem;
}

.min-h-\[12rem\] {
  min-height: 12rem;
}

.min-h-\[18\.75rem\] {
  min-height: 18.75rem;
}

.min-h-\[1rem\] {
  min-height: 1rem;
}

.min-h-\[2\.5rem\] {
  min-height: 2.5rem;
}

.min-h-\[2\.8rem\] {
  min-height: 2.8rem;
}

.min-h-\[2rem\] {
  min-height: 2rem;
}

.min-h-\[3\.125rem\] {
  min-height: 3.125rem;
}

.min-h-\[3\.25rem\] {
  min-height: 3.25rem;
}

.min-h-\[305px\] {
  min-height: 305px;
}

.min-h-\[31\.25rem\] {
  min-height: 31.25rem;
}

.min-h-\[325px\] {
  min-height: 325px;
}

.min-h-\[3rem\] {
  min-height: 3rem;
}

.min-h-\[4\.5rem\] {
  min-height: 4.5rem;
}

.min-h-\[40px\] {
  min-height: 40px;
}

.min-h-\[50\%\] {
  min-height: 50%;
}

.min-h-\[50px\] {
  min-height: 50px;
}

.min-h-\[50vh\] {
  min-height: 50vh;
}

.min-h-\[6\.25rem\] {
  min-height: 6.25rem;
}

.min-h-\[60vh\] {
  min-height: 60vh;
}

.min-h-\[70\%\] {
  min-height: 70%;
}

.min-h-\[70px\] {
  min-height: 70px;
}

.min-h-\[90\%\] {
  min-height: 90%;
}

.min-h-\[auto\] {
  min-height: auto;
}

.min-h-fit {
  min-height: -moz-fit-content;
  min-height: fit-content;
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.min-h-svh {
  min-height: 100svh;
}

.\!w-1\/2 {
  width: 50% !important;
}

.\!w-4 {
  width: 1rem !important;
}

.\!w-6 {
  width: 1.5rem !important;
}

.\!w-\[11rem\] {
  width: 11rem !important;
}

.\!w-\[2\.0713rem\] {
  width: 2.0713rem !important;
}

.\!w-\[2\.65rem\] {
  width: 2.65rem !important;
}

.\!w-\[auto\] {
  width: auto !important;
}

.\!w-fit {
  width: -moz-fit-content !important;
  width: fit-content !important;
}

.\!w-full {
  width: 100% !important;
}

.\!w-px {
  width: 1px !important;
}

.w-0 {
  width: 0px;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-11\/12 {
  width: 91.666667%;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-96 {
  width: 24rem;
}

.w-\[\.9375rem\] {
  width: .9375rem;
}

.w-\[0\.9375rem\] {
  width: 0.9375rem;
}

.w-\[1\.0313rem\] {
  width: 1.0313rem;
}

.w-\[1\.125rem\] {
  width: 1.125rem;
}

.w-\[1\.2381rem\] {
  width: 1.2381rem;
}

.w-\[1\.25rem\] {
  width: 1.25rem;
}

.w-\[1\.4rem\] {
  width: 1.4rem;
}

.w-\[1\.5rem\] {
  width: 1.5rem;
}

.w-\[10\.375rem\] {
  width: 10.375rem;
}

.w-\[100\%\] {
  width: 100%;
}

.w-\[12\.5rem\] {
  width: 12.5rem;
}

.w-\[15\%\] {
  width: 15%;
}

.w-\[15\.6875rem\] {
  width: 15.6875rem;
}

.w-\[150px\] {
  width: 150px;
}

.w-\[15px\] {
  width: 15px;
}

.w-\[15rem\] {
  width: 15rem;
}

.w-\[160px\] {
  width: 160px;
}

.w-\[18\.75rem\] {
  width: 18.75rem;
}

.w-\[18px\] {
  width: 18px;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[1rem\] {
  width: 1rem;
}

.w-\[20\%\] {
  width: 20%;
}

.w-\[23rem\] {
  width: 23rem;
}

.w-\[25rem\] {
  width: 25rem;
}

.w-\[260px\] {
  width: 260px;
}

.w-\[2px\] {
  width: 2px;
}

.w-\[2rem\] {
  width: 2rem;
}

.w-\[3\.625rem\] {
  width: 3.625rem;
}

.w-\[3\.75rem\] {
  width: 3.75rem;
}

.w-\[300px\] {
  width: 300px;
}

.w-\[304px\] {
  width: 304px;
}

.w-\[30px\] {
  width: 30px;
}

.w-\[328px\] {
  width: 328px;
}

.w-\[32px\] {
  width: 32px;
}

.w-\[3rem\] {
  width: 3rem;
}

.w-\[4\.2rem\] {
  width: 4.2rem;
}

.w-\[40\%\] {
  width: 40%;
}

.w-\[400px\] {
  width: 400px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[45\%\] {
  width: 45%;
}

.w-\[450px\] {
  width: 450px;
}

.w-\[470px\] {
  width: 470px;
}

.w-\[4px\] {
  width: 4px;
}

.w-\[5\.625rem\] {
  width: 5.625rem;
}

.w-\[50\%\] {
  width: 50%;
}

.w-\[50px\] {
  width: 50px;
}

.w-\[5rem\] {
  width: 5rem;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[600px\] {
  width: 600px;
}

.w-\[668px\] {
  width: 668px;
}

.w-\[6px\] {
  width: 6px;
}

.w-\[70\%\] {
  width: 70%;
}

.w-\[70px\] {
  width: 70px;
}

.w-\[72px\] {
  width: 72px;
}

.w-\[76px\] {
  width: 76px;
}

.w-\[80\%\] {
  width: 80%;
}

.w-\[85\%\] {
  width: 85%;
}

.w-\[868px\] {
  width: 868px;
}

.w-\[9\.375rem\] {
  width: 9.375rem;
}

.w-\[90\%\] {
  width: 90%;
}

.w-\[auto\] {
  width: auto;
}

.w-\[calc\(100\%-100px\)\] {
  width: calc(100% - 100px);
}

.w-\[calc\(100\%-50px\)\] {
  width: calc(100% - 50px);
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.w-screen {
  width: 100vw;
}

.\!min-w-\[11rem\] {
  min-width: 11rem !important;
}

.\!min-w-\[2\.65rem\] {
  min-width: 2.65rem !important;
}

.\!min-w-\[6\.25rem\] {
  min-width: 6.25rem !important;
}

.\!min-w-fit {
  min-width: -moz-fit-content !important;
  min-width: fit-content !important;
}

.\!min-w-full {
  min-width: 100% !important;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-2 {
  min-width: 0.5rem;
}

.min-w-5 {
  min-width: 1.25rem;
}

.min-w-\[1\.5rem\] {
  min-width: 1.5rem;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.min-w-\[12\.5rem\] {
  min-width: 12.5rem;
}

.min-w-\[15rem\] {
  min-width: 15rem;
}

.min-w-\[18\.75rem\] {
  min-width: 18.75rem;
}

.min-w-\[2\.5rem\] {
  min-width: 2.5rem;
}

.min-w-\[20px\] {
  min-width: 20px;
}

.min-w-\[23rem\] {
  min-width: 23rem;
}

.min-w-\[25rem\] {
  min-width: 25rem;
}

.min-w-\[2rem\] {
  min-width: 2rem;
}

.min-w-\[310px\] {
  min-width: 310px;
}

.min-w-\[4\.2rem\] {
  min-width: 4.2rem;
}

.min-w-\[48px\] {
  min-width: 48px;
}

.min-w-\[5\.625rem\] {
  min-width: 5.625rem;
}

.min-w-\[5rem\] {
  min-width: 5rem;
}

.min-w-\[6\.25rem\] {
  min-width: 6.25rem;
}

.min-w-\[6\.6094rem\] {
  min-width: 6.6094rem;
}

.min-w-\[6\.8125rem\] {
  min-width: 6.8125rem;
}

.min-w-\[64px\] {
  min-width: 64px;
}

.min-w-\[70\%\] {
  min-width: 70%;
}

.min-w-fit {
  min-width: -moz-fit-content;
  min-width: fit-content;
}

.min-w-full {
  min-width: 100%;
}

.\!max-w-\[11rem\] {
  max-width: 11rem !important;
}

.\!max-w-\[2\.65rem\] {
  max-width: 2.65rem !important;
}

.\!max-w-\[auto\] {
  max-width: auto !important;
}

.\!max-w-fit {
  max-width: -moz-fit-content !important;
  max-width: fit-content !important;
}

.\!max-w-full {
  max-width: 100% !important;
}

.max-w-2 {
  max-width: 0.5rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[1\.5rem\] {
  max-width: 1.5rem;
}

.max-w-\[12\.5rem\] {
  max-width: 12.5rem;
}

.max-w-\[15rem\] {
  max-width: 15rem;
}

.max-w-\[18\.75rem\] {
  max-width: 18.75rem;
}

.max-w-\[2\.5rem\] {
  max-width: 2.5rem;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-\[267px\] {
  max-width: 267px;
}

.max-w-\[325px\] {
  max-width: 325px;
}

.max-w-\[4\.2rem\] {
  max-width: 4.2rem;
}

.max-w-\[5rem\] {
  max-width: 5rem;
}

.max-w-\[80\%\] {
  max-width: 80%;
}

.max-w-\[9\.375rem\] {
  max-width: 9.375rem;
}

.max-w-\[90\%\] {
  max-width: 90%;
}

.max-w-\[auto\] {
  max-width: auto;
}

.max-w-\[calc\(100\%-1rem\)\] {
  max-width: calc(100% - 1rem);
}

.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-\[30\%\] {
  flex: 30%;
}

.flex-\[50\%\] {
  flex: 50%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.\!grow {
  flex-grow: 1 !important;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.basis-auto {
  flex-basis: auto;
}

.origin-\[0_0\] {
  transform-origin: 0 0;
}

.origin-\[50\%_50\%\] {
  transform-origin: 50% 50%;
}

.origin-\[center_bottom_0\] {
  transform-origin: center bottom 0;
}

.origin-bottom {
  transform-origin: bottom;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-0 {
  --tw-translate-x: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[6px\] {
  --tw-translate-x: -6px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-10\%\] {
  --tw-translate-x: -10%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[150\%\] {
  --tw-translate-x: 150%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[1\.89rem\] {
  --tw-translate-y: 1.89rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[6px\] {
  --tw-translate-y: 6px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[0\.25\] {
  --tw-scale-x: 0.25;
  --tw-scale-y: 0.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[1\.02\] {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-y-\[0\.8\] {
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-none {
  transform: none;
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(-3deg);
  }

  50% {
    transform: rotate(3deg);
  }
}

.\!animate-wiggle {
  animation: wiggle 200ms ease-in-out !important;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.animate-\[fade-in_0\.15s_both\] {
  animation: fade-in 0.15s both;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.animate-\[fade-in_0\.3s_both\] {
  animation: fade-in 0.3s both;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.animate-\[fade-in_350ms_ease-in-out\] {
  animation: fade-in 350ms ease-in-out;
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.animate-\[fade-out_0\.15s_both\] {
  animation: fade-out 0.15s both;
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.animate-\[fade-out_0\.3s_both\] {
  animation: fade-out 0.3s both;
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.animate-\[fade-out_350ms_ease-in-out\] {
  animation: fade-out 350ms ease-in-out;
}

@keyframes progress {
  0% {
    transform: translateX(-45%);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate-\[progress_3s_ease-in-out_infinite\] {
  animation: progress 3s ease-in-out infinite;
}

@keyframes show-up-clock {
  0% {
    opacity: 0;
    transform: scale(0.7);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-\[show-up-clock_350ms_linear\] {
  animation: show-up-clock 350ms linear;
}

@keyframes slide-in-left {
  0% {
    visibility: visible;
    transform: translate3d(-100%, 0, 0);
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

.animate-\[slide-in-left_0\.8s_both\] {
  animation: slide-in-left 0.8s both;
}

@keyframes slide-in-right {
  0% {
    visibility: visible;
    transform: translate3d(100%, 0, 0);
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

.animate-\[slide-in-right_0\.8s_both\] {
  animation: slide-in-right 0.8s both;
}

@keyframes slide-out-left {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
  }
}

.animate-\[slide-out-left_0\.8s_both\] {
  animation: slide-out-left 0.8s both;
}

@keyframes slide-out-right {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}

.animate-\[slide-out-right_0\.8s_both\] {
  animation: slide-out-right 0.8s both;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }

  50% {
    transform: none;
    opacity: 1;
  }
}

.animate-\[spinner-grow_0\.75s_linear_infinite\] {
  animation: spinner-grow 0.75s linear infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(-3deg);
  }

  50% {
    transform: rotate(3deg);
  }
}

.animate-wiggle {
  animation: wiggle 200ms ease-in-out;
}

.\!cursor-default {
  cursor: default !important;
}

.\!cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-default {
  cursor: default;
}

.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}

.cursor-none {
  cursor: none;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-text {
  cursor: text;
}

.touch-none {
  touch-action: none;
}

.touch-pan-y {
  --tw-pan-y: pan-y;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.list-decimal {
  list-style-type: decimal;
}

.list-none {
  list-style-type: none;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-\[1fr_auto\] {
  grid-template-columns: 1fr auto;
}

.grid-cols-\[repeat\(auto-fill\2c minmax\(10\.5rem\2c 1fr\)\)\] {
  grid-template-columns: repeat(auto-fill,minmax(10.5rem,1fr));
}

.\!grid-rows-\[auto_auto\] {
  grid-template-rows: auto auto !important;
}

.grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr));
}

.grid-rows-\[1fr_auto\] {
  grid-template-rows: 1fr auto;
}

.grid-rows-\[75\%_20\%\] {
  grid-template-rows: 75% 20%;
}

.grid-rows-\[90\%_10\%\] {
  grid-template-rows: 90% 10%;
}

.grid-rows-\[auto_1fr\] {
  grid-template-rows: auto 1fr;
}

.grid-rows-\[auto_1fr_auto\] {
  grid-template-rows: auto 1fr auto;
}

.grid-rows-\[auto_1fr_auto_auto\] {
  grid-template-rows: auto 1fr auto auto;
}

.grid-rows-\[auto_85\%\] {
  grid-template-rows: auto 85%;
}

.grid-rows-\[auto_90\%\] {
  grid-template-rows: auto 90%;
}

.grid-rows-\[auto_auto\] {
  grid-template-rows: auto auto;
}

.grid-rows-\[auto_auto_auto\] {
  grid-template-rows: auto auto auto;
}

.\!flex-row {
  flex-direction: row !important;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-center {
  align-content: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-stretch {
  align-items: stretch;
}

.justify-normal {
  justify-content: normal;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.\!justify-center {
  justify-content: center !important;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.justify-items-center {
  justify-items: center;
}

.\!gap-0 {
  gap: 0px !important;
}

.gap-0 {
  gap: 0px;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-7 {
  gap: 1.75rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[\.25rem\] {
  gap: .25rem;
}

.gap-\[\.3125rem\] {
  gap: .3125rem;
}

.gap-\[\.375rem\] {
  gap: .375rem;
}

.gap-\[\.5rem\] {
  gap: .5rem;
}

.gap-\[\.75rem\] {
  gap: .75rem;
}

.gap-\[1\.5rem\] {
  gap: 1.5rem;
}

.gap-\[1rem\] {
  gap: 1rem;
}

.gap-\[2\.5rem\] {
  gap: 2.5rem;
}

.gap-\[2rem\] {
  gap: 2rem;
}

.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.gap-x-5 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-x > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-\[\#1F1D1A1A\] > :not([hidden]) ~ :not([hidden]) {
  border-color: #1F1D1A1A;
}

.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}

.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity, 1));
}

.divide-white > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-divide-opacity, 1));
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.overflow-auto {
  overflow: auto;
}

.\!overflow-hidden {
  overflow: hidden !important;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-clip {
  overflow: clip;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

.whitespace-normal {
  white-space: normal;
}

.\!whitespace-nowrap {
  white-space: nowrap !important;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.break-normal {
  overflow-wrap: normal;
  word-break: normal;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.\!rounded-\[\.125rem\] {
  border-radius: .125rem !important;
}

.\!rounded-\[\.5rem\] {
  border-radius: .5rem !important;
}

.\!rounded-\[\.625rem\] {
  border-radius: .625rem !important;
}

.\!rounded-\[0\.125rem\] {
  border-radius: 0.125rem !important;
}

.\!rounded-full {
  border-radius: 9999px !important;
}

.\!rounded-lg {
  border-radius: 0.5rem !important;
}

.\!rounded-md {
  border-radius: 0.375rem !important;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-\[\.125rem\] {
  border-radius: .125rem;
}

.rounded-\[\.375rem\] {
  border-radius: .375rem;
}

.rounded-\[\.5rem\] {
  border-radius: .5rem;
}

.rounded-\[\.625rem\] {
  border-radius: .625rem;
}

.rounded-\[\.75rem\] {
  border-radius: .75rem;
}

.rounded-\[\.875rem\] {
  border-radius: .875rem;
}

.rounded-\[0\.25rem\] {
  border-radius: 0.25rem;
}

.rounded-\[0\.5rem\] {
  border-radius: 0.5rem;
}

.rounded-\[0\.6rem\] {
  border-radius: 0.6rem;
}

.rounded-\[0rem_1\.25rem\] {
  border-radius: 0rem 1.25rem;
}

.rounded-\[1\.25rem\] {
  border-radius: 1.25rem;
}

.rounded-\[1\.25rem_1\.25rem_0rem_1\.25rem\] {
  border-radius: 1.25rem 1.25rem 0rem 1.25rem;
}

.rounded-\[100\%\] {
  border-radius: 100%;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[16px\] {
  border-radius: 16px;
}

.rounded-\[50\%\] {
  border-radius: 50%;
}

.rounded-\[50px\] {
  border-radius: 50px;
}

.rounded-\[6px\] {
  border-radius: 6px;
}

.rounded-\[8px\] {
  border-radius: 8px;
}

.rounded-\[999px\] {
  border-radius: 999px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-l-\[0\.25rem\] {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-r-\[0\.25rem\] {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.rounded-t-\[\.75rem\] {
  border-top-left-radius: .75rem;
  border-top-right-radius: .75rem;
}

.rounded-t-\[0\.6rem\] {
  border-top-left-radius: 0.6rem;
  border-top-right-radius: 0.6rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-t-sm {
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
}

.rounded-bl-lg {
  border-bottom-left-radius: 0.5rem;
}

.rounded-bl-none {
  border-bottom-left-radius: 0px;
}

.rounded-br-2xl {
  border-bottom-right-radius: 1rem;
}

.rounded-br-lg {
  border-bottom-right-radius: 0.5rem;
}

.rounded-tl-2xl {
  border-top-left-radius: 1rem;
}

.rounded-tr-2xl {
  border-top-right-radius: 1rem;
}

.\!border {
  border-width: 1px !important;
}

.\!border-0 {
  border-width: 0px !important;
}

.\!border-\[3px\] {
  border-width: 3px !important;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-\[\.125rem\] {
  border-width: .125rem;
}

.border-\[0\.125rem\] {
  border-width: 0.125rem;
}

.border-\[0\.15em\] {
  border-width: 0.15em;
}

.border-\[14px\] {
  border-width: 14px;
}

.border-\[1px\] {
  border-width: 1px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.\!border-b {
  border-bottom-width: 1px !important;
}

.\!border-r {
  border-right-width: 1px !important;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-0 {
  border-left-width: 0px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-l-\[0\.125rem\] {
  border-left-width: 0.125rem;
}

.border-l-\[3px\] {
  border-left-width: 3px;
}

.border-r {
  border-right-width: 1px;
}

.border-r-0 {
  border-right-width: 0px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-t-2 {
  border-top-width: 2px;
}

.\!border-solid {
  border-style: solid !important;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.\!border-none {
  border-style: none !important;
}

.border-none {
  border-style: none;
}

.\!border-\[\#14a44d\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1)) !important;
}

.\!border-\[\#b2b3b4\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(178 179 180 / var(--tw-border-opacity, 1)) !important;
}

.\!border-\[\#dc4c64\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1)) !important;
}

.\!border-\[\#e25679\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(226 86 121 / var(--tw-border-opacity, 1)) !important;
}

.\!border-black {
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.\!border-red-500 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1)) !important;
}

.border-\[\#14a44d\] {
  --tw-border-opacity: 1;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1));
}

.border-\[\#1f1d1a\] {
  --tw-border-opacity: 1;
  border-color: rgb(31 29 26 / var(--tw-border-opacity, 1));
}

.border-\[\#3b71ca\] {
  --tw-border-opacity: 1;
  border-color: rgb(59 113 202 / var(--tw-border-opacity, 1));
}

.border-\[\#C6C6C6\] {
  --tw-border-opacity: 1;
  border-color: rgb(198 198 198 / var(--tw-border-opacity, 1));
}

.border-\[\#DC5A5D\] {
  --tw-border-opacity: 1;
  border-color: rgb(220 90 93 / var(--tw-border-opacity, 1));
}

.border-\[\#E0E0E0\] {
  --tw-border-opacity: 1;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}

.border-\[\#a8a8a8\] {
  --tw-border-opacity: 1;
  border-color: rgb(168 168 168 / var(--tw-border-opacity, 1));
}

.border-\[\#ca8a04\] {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}

.border-\[\#d8dae5\] {
  --tw-border-opacity: 1;
  border-color: rgb(216 218 229 / var(--tw-border-opacity, 1));
}

.border-\[\#dc4c64\] {
  --tw-border-opacity: 1;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1));
}

.border-\[\#eee\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity, 1));
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-black\/10 {
  border-color: rgb(0 0 0 / 0.1);
}

.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-current {
  border-color: currentColor;
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.border-neutral-100 {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));
}

.border-neutral-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}

.border-neutral-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-yellow-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}

.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}

.border-b-\[\#C6C6C6\] {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(198 198 198 / var(--tw-border-opacity, 1));
}

.border-b-\[\#E0E0E0\] {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}

.border-b-black\/20 {
  border-bottom-color: rgb(0 0 0 / 0.2);
}

.border-b-gray-400 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.border-b-transparent {
  border-bottom-color: transparent;
}

.border-r-transparent {
  border-right-color: transparent;
}

.border-t-blue-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-t-transparent {
  border-top-color: transparent;
}

.\!bg-\[\#858585\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(133 133 133 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-\[\#DC2626\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-\[\#e25679\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(226 86 121 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-\[\#ebebeb\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(235 235 235 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-black {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-danger-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(250 229 233 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-green-500 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-neutral-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-primary {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-red-500 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-success-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(214 250 228 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-transparent {
  background-color: transparent !important;
}

.\!bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-yellow-700 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#00000012\] {
  background-color: #00000012;
}

.bg-\[\#00000066\] {
  background-color: #00000066;
}

.bg-\[\#00000099\] {
  background-color: #00000099;
}

.bg-\[\#000000e6\] {
  background-color: #000000e6;
}

.bg-\[\#16c66c\] {
  --tw-bg-opacity: 1;
  background-color: rgb(22 198 108 / var(--tw-bg-opacity, 1));
}

.bg-\[\#292828d2\] {
  background-color: #292828d2;
}

.bg-\[\#3b71ca\] {
  --tw-bg-opacity: 1;
  background-color: rgb(59 113 202 / var(--tw-bg-opacity, 1));
}

.bg-\[\#4F46E5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-\[\#6366F1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}

.bg-\[\#6d6d6d\] {
  --tw-bg-opacity: 1;
  background-color: rgb(109 109 109 / var(--tw-bg-opacity, 1));
}

.bg-\[\#D1FAE5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}

.bg-\[\#DC5A5D\] {
  --tw-bg-opacity: 1;
  background-color: rgb(220 90 93 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F4F4F4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 244 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F9F9F9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 249 249 / var(--tw-bg-opacity, 1));
}

.bg-\[\#aaa\] {
  --tw-bg-opacity: 1;
  background-color: rgb(170 170 170 / var(--tw-bg-opacity, 1));
}

.bg-\[\#eceff1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}

.bg-\[\#eee\] {
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1));
}

.bg-\[\#f4f4f4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 244 / var(--tw-bg-opacity, 1));
}

.bg-\[\#f9f9f9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 249 249 / var(--tw-bg-opacity, 1));
}

.bg-\[rgb\(255\2c 255\2c 255\2c 0\.6\)\] {
  background-color: rgb(255,255,255,0.6);
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.4\)\] {
  background-color: rgba(0,0,0,0.4);
}

.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.bg-current {
  background-color: currentColor;
}

.bg-cyan-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}

.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}

.bg-fuchsia-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 232 255 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-inherit {
  background-color: inherit;
}

.bg-lime-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity, 1));
}

.bg-neutral-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
}

.bg-neutral-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-pink-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}

.bg-pink-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-primary-light {
  background-color: #4F46E550;
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-50\/30 {
  background-color: rgb(254 242 242 / 0.3);
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-rose-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 230 / var(--tw-bg-opacity, 1));
}

.bg-sky-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.bg-slate-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}

.bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}

.bg-slate-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.bg-stone-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1));
}

.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-violet-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 233 254 / var(--tw-bg-opacity, 1));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50\/30 {
  background-color: rgb(254 252 232 / 0.3);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-zinc-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
}

.bg-zinc-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}

.bg-zinc-600\/50 {
  background-color: rgb(82 82 91 / 0.5);
}

.bg-zinc-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}

.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}

.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-60 {
  --tw-bg-opacity: 0.6;
}

.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-none {
  background-image: none;
}

.from-\[\#262626\] {
  --tw-gradient-from: #262626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(38 38 38 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-900 {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-\[\#525252\] {
  --tw-gradient-to: #525252 var(--tw-gradient-to-position);
}

.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}

.bg-cover {
  background-size: cover;
}

.bg-clip-padding {
  background-clip: padding-box;
}

.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-\[\#afafaf\] {
  fill: #afafaf;
}

.fill-current {
  fill: currentColor;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.\!p-0 {
  padding: 0px !important;
}

.p-0 {
  padding: 0px;
}

.p-0\.5 {
  padding: 0.125rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-12 {
  padding: 3rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-20 {
  padding: 5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-3\.5 {
  padding: 0.875rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-9 {
  padding: 2.25rem;
}

.p-\[\.25rem\] {
  padding: .25rem;
}

.p-\[\.25rem_\.5rem_\.25rem_\.25rem\] {
  padding: .25rem .5rem .25rem .25rem;
}

.p-\[\.3125rem\] {
  padding: .3125rem;
}

.p-\[\.625rem\] {
  padding: .625rem;
}

.p-\[\.75rem_1rem_\.75rem_1rem\] {
  padding: .75rem 1rem .75rem 1rem;
}

.p-\[1\.5rem\] {
  padding: 1.5rem;
}

.p-\[10px\] {
  padding: 10px;
}

.p-\[1rem\] {
  padding: 1rem;
}

.p-\[2rem\] {
  padding: 2rem;
}

.p-\[5px\] {
  padding: 5px;
}

.p-\[auto\] {
  padding: auto;
}

.\!px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.\!px-10 {
  padding-left: 2.5rem !important;
  padding-right: 2.5rem !important;
}

.\!px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.\!px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.\!px-5 {
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
}

.\!px-\[\.0625rem\] {
  padding-left: .0625rem !important;
  padding-right: .0625rem !important;
}

.\!py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.\!py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.\!py-\[0\.5rem\] {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-\[\.6125rem\] {
  padding-left: .6125rem;
  padding-right: .6125rem;
}

.px-\[\.75rem\] {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-\[0\.4rem\] {
  padding-left: 0.4rem;
  padding-right: 0.4rem;
}

.px-\[1\.4rem\] {
  padding-left: 1.4rem;
  padding-right: 1.4rem;
}

.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px;
}

.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px;
}

.px-\[14px\] {
  padding-left: 14px;
  padding-right: 14px;
}

.px-\[17px\] {
  padding-left: 17px;
  padding-right: 17px;
}

.px-\[20px\] {
  padding-left: 20px;
  padding-right: 20px;
}

.px-\[40px\] {
  padding-left: 40px;
  padding-right: 40px;
}

.px-\[auto\] {
  padding-left: auto;
  padding-right: auto;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}

.py-\[\.375rem\] {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-\[\.5625rem\] {
  padding-top: .5625rem;
  padding-bottom: .5625rem;
}

.py-\[\.5rem\] {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-\[\.75rem\] {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-\[0\.32rem\] {
  padding-top: 0.32rem;
  padding-bottom: 0.32rem;
}

.py-\[0\.33rem\] {
  padding-top: 0.33rem;
  padding-bottom: 0.33rem;
}

.py-\[0\.4375rem\] {
  padding-top: 0.4375rem;
  padding-bottom: 0.4375rem;
}

.py-\[0\.4rem\] {
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
}

.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-\[1px\] {
  padding-top: 1px;
  padding-bottom: 1px;
}

.py-\[1rem\] {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-\[4rem\] {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}

.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}

.\!pt-0 {
  padding-top: 0px !important;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-\[\.5rem\] {
  padding-bottom: .5rem;
}

.pb-\[10px\] {
  padding-bottom: 10px;
}

.pb-\[5px\] {
  padding-bottom: 5px;
}

.pl-0 {
  padding-left: 0px;
}

.pl-0\.5 {
  padding-left: 0.125rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-1\.5 {
  padding-left: 0.375rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-7 {
  padding-left: 1.75rem;
}

.pl-9 {
  padding-left: 2.25rem;
}

.pl-\[\.75rem\] {
  padding-left: .75rem;
}

.pl-\[1\.5rem\] {
  padding-left: 1.5rem;
}

.pl-\[18px\] {
  padding-left: 18px;
}

.pl-\[1rem\] {
  padding-left: 1rem;
}

.pl-\[50px\] {
  padding-left: 50px;
}

.pl-\[8px\] {
  padding-left: 8px;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-5 {
  padding-right: 1.25rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pr-9 {
  padding-right: 2.25rem;
}

.pr-\[\.875rem\] {
  padding-right: .875rem;
}

.pr-\[1rem\] {
  padding-right: 1rem;
}

.pr-\[24px\] {
  padding-right: 24px;
}

.pt-0 {
  padding-top: 0px;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-7 {
  padding-top: 1.75rem;
}

.pt-\[\.5rem\] {
  padding-top: .5rem;
}

.pt-\[0\.37rem\] {
  padding-top: 0.37rem;
}

.pt-\[6px\] {
  padding-top: 6px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

.text-start {
  text-align: start;
}

.align-baseline {
  vertical-align: baseline;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.align-text-bottom {
  vertical-align: text-bottom;
}

.align-\[-0\.125em\] {
  vertical-align: -0.125em;
}

.font-\[\'Inter\'\] {
  font-family: 'Inter';
}

.font-sans {
  font-family: "Core Sans CR", sans-serif;
}

.\!text-\[1\.5rem\] {
  font-size: 1.5rem !important;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}

.text-\[\.40rem\] {
  font-size: .40rem;
}

.text-\[\.75rem\] {
  font-size: .75rem;
}

.text-\[\.8125rem\] {
  font-size: .8125rem;
}

.text-\[\.825rem\] {
  font-size: .825rem;
}

.text-\[\.875rem\] {
  font-size: .875rem;
}

.text-\[\.8rem\] {
  font-size: .8rem;
}

.text-\[\.9375rem\] {
  font-size: .9375rem;
}

.text-\[0\.8rem\] {
  font-size: 0.8rem;
}

.text-\[0\.9rem\] {
  font-size: 0.9rem;
}

.text-\[1\.125rem\] {
  font-size: 1.125rem;
}

.text-\[1\.1rem\] {
  font-size: 1.1rem;
}

.text-\[1\.25rem\] {
  font-size: 1.25rem;
}

.text-\[1\.375rem\] {
  font-size: 1.375rem;
}

.text-\[1\.5rem\] {
  font-size: 1.5rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[1rem\] {
  font-size: 1rem;
}

.text-\[2\.5rem\] {
  font-size: 2.5rem;
}

.text-\[2\.8125rem\] {
  font-size: 2.8125rem;
}

.text-\[2rem\] {
  font-size: 2rem;
}

.text-\[3\.75rem\] {
  font-size: 3.75rem;
}

.text-\[34px\] {
  font-size: 34px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-\[400\] {
  font-weight: 400;
}

.font-\[500\] {
  font-weight: 500;
}

.font-\[600\] {
  font-weight: 600;
}

.font-\[700\] {
  font-weight: 700;
}

.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.\!capitalize {
  text-transform: capitalize !important;
}

.capitalize {
  text-transform: capitalize;
}

.normal-case {
  text-transform: none;
}

.italic {
  font-style: italic;
}

.not-italic {
  font-style: normal;
}

.\!leading-tight {
  line-height: 1.25 !important;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-9 {
  line-height: 2.25rem;
}

.leading-\[\.9375rem\] {
  line-height: .9375rem;
}

.leading-\[0\.5rem_\!important\] {
  line-height: 0.5rem !important;
}

.leading-\[1\.125rem\] {
  line-height: 1.125rem;
}

.leading-\[1\.25rem\] {
  line-height: 1.25rem;
}

.leading-\[1\.2\] {
  line-height: 1.2;
}

.leading-\[1\.5\] {
  line-height: 1.5;
}

.leading-\[1\.5rem\] {
  line-height: 1.5rem;
}

.leading-\[1\.6\] {
  line-height: 1.6;
}

.leading-\[1\.75rem\] {
  line-height: 1.75rem;
}

.leading-\[1rem\] {
  line-height: 1rem;
}

.leading-\[2\.15\] {
  line-height: 2.15;
}

.leading-\[40px\] {
  line-height: 40px;
}

.leading-\[50px\] {
  line-height: 50px;
}

.leading-loose {
  line-height: 2;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-\[-0\.006em\] {
  letter-spacing: -0.006em;
}

.tracking-\[-0\.00833em\] {
  letter-spacing: -0.00833em;
}

.tracking-\[-0\.011em\] {
  letter-spacing: -0.011em;
}

.tracking-\[-0\.6\%\] {
  letter-spacing: -0.6%;
}

.tracking-\[-1\.5\%\] {
  letter-spacing: -1.5%;
}

.tracking-\[\.0313rem\] {
  letter-spacing: .0313rem;
}

.tracking-\[\.1rem\] {
  letter-spacing: .1rem;
}

.tracking-\[0\.1rem\] {
  letter-spacing: 0.1rem;
}

.tracking-\[1\.7px\] {
  letter-spacing: 1.7px;
}

.tracking-normal {
  letter-spacing: 0em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.\!text-\[\#14a44d\] {
  --tw-text-opacity: 1 !important;
  color: rgb(20 164 77 / var(--tw-text-opacity, 1)) !important;
}

.\!text-\[\#dc4c64\] {
  --tw-text-opacity: 1 !important;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1)) !important;
}

.\!text-black {
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) !important;
}

.\!text-danger-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(176 35 58 / var(--tw-text-opacity, 1)) !important;
}

.\!text-gray-50 {
  --tw-text-opacity: 1 !important;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1)) !important;
}

.\!text-gray-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1)) !important;
}

.\!text-primary {
  --tw-text-opacity: 1 !important;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1)) !important;
}

.\!text-red-500 {
  --tw-text-opacity: 1 !important;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) !important;
}

.\!text-success-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(14 117 55 / var(--tw-text-opacity, 1)) !important;
}

.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.text-\[\#065F46\] {
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}

.text-\[\#14a44d\] {
  --tw-text-opacity: 1;
  color: rgb(20 164 77 / var(--tw-text-opacity, 1));
}

.text-\[\#18181B\] {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}

.text-\[\#1f1d1a\] {
  --tw-text-opacity: 1;
  color: rgb(31 29 26 / var(--tw-text-opacity, 1));
}

.text-\[\#212529\] {
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity, 1));
}

.text-\[\#262626\] {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.text-\[\#292829fd\] {
  color: #292829fd;
}

.text-\[\#333333\] {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity, 1));
}

.text-\[\#344054\] {
  --tw-text-opacity: 1;
  color: rgb(52 64 84 / var(--tw-text-opacity, 1));
}

.text-\[\#38C793\] {
  --tw-text-opacity: 1;
  color: rgb(56 199 147 / var(--tw-text-opacity, 1));
}

.text-\[\#393939\] {
  --tw-text-opacity: 1;
  color: rgb(57 57 57 / var(--tw-text-opacity, 1));
}

.text-\[\#3b71ca\] {
  --tw-text-opacity: 1;
  color: rgb(59 113 202 / var(--tw-text-opacity, 1));
}

.text-\[\#4F46E5\] {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-\[\#4f4f4f\] {
  --tw-text-opacity: 1;
  color: rgb(79 79 79 / var(--tw-text-opacity, 1));
}

.text-\[\#525252\] {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.text-\[\#636363\] {
  --tw-text-opacity: 1;
  color: rgb(99 99 99 / var(--tw-text-opacity, 1));
}

.text-\[\#6366F1\] {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.text-\[\#667085\] {
  --tw-text-opacity: 1;
  color: rgb(102 112 133 / var(--tw-text-opacity, 1));
}

.text-\[\#6F6F6F\] {
  --tw-text-opacity: 1;
  color: rgb(111 111 111 / var(--tw-text-opacity, 1));
}

.text-\[\#8D8D8D\] {
  --tw-text-opacity: 1;
  color: rgb(141 141 141 / var(--tw-text-opacity, 1));
}

.text-\[\#A8A8A8\] {
  --tw-text-opacity: 1;
  color: rgb(168 168 168 / var(--tw-text-opacity, 1));
}

.text-\[\#CE0000\] {
  --tw-text-opacity: 1;
  color: rgb(206 0 0 / var(--tw-text-opacity, 1));
}

.text-\[\#b3afaf\] {
  --tw-text-opacity: 1;
  color: rgb(179 175 175 / var(--tw-text-opacity, 1));
}

.text-\[\#b3b3b3\] {
  --tw-text-opacity: 1;
  color: rgb(179 179 179 / var(--tw-text-opacity, 1));
}

.text-\[\#ca8a04\] {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-\[\#dc4c64\] {
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.text-\[\#ffffff8a\] {
  color: #ffffff8a;
}

.text-\[base\] {
  color: base;
}

.text-\[rgb\(220\2c 76\2c 100\)\] {
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.text-\[white\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-black\/50 {
  color: rgb(0 0 0 / 0.5);
}

.text-black\/\[64\] {
  color: rgb(0 0 0 / 64);
}

.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-blue-950 {
  --tw-text-opacity: 1;
  color: rgb(23 37 84 / var(--tw-text-opacity, 1));
}

.text-cyan-600 {
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}

.text-danger {
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}

.text-fuchsia-600 {
  --tw-text-opacity: 1;
  color: rgb(192 38 211 / var(--tw-text-opacity, 1));
}

.text-fuchsia-800 {
  --tw-text-opacity: 1;
  color: rgb(134 25 143 / var(--tw-text-opacity, 1));
}

.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.text-info {
  --tw-text-opacity: 1;
  color: rgb(84 180 211 / var(--tw-text-opacity, 1));
}

.text-lime-600 {
  --tw-text-opacity: 1;
  color: rgb(101 163 13 / var(--tw-text-opacity, 1));
}

.text-neutral-200 {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity, 1));
}

.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.text-neutral-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-pink-500 {
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity, 1));
}

.text-pink-600 {
  --tw-text-opacity: 1;
  color: rgb(219 39 119 / var(--tw-text-opacity, 1));
}

.text-pink-700 {
  --tw-text-opacity: 1;
  color: rgb(190 24 93 / var(--tw-text-opacity, 1));
}

.text-pink-800 {
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-rose-600 {
  --tw-text-opacity: 1;
  color: rgb(225 29 72 / var(--tw-text-opacity, 1));
}

.text-rose-800 {
  --tw-text-opacity: 1;
  color: rgb(159 18 57 / var(--tw-text-opacity, 1));
}

.text-secondary {
  --tw-text-opacity: 1;
  color: rgb(159 166 178 / var(--tw-text-opacity, 1));
}

.text-sky-600 {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}

.text-sky-700 {
  --tw-text-opacity: 1;
  color: rgb(3 105 161 / var(--tw-text-opacity, 1));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.text-stone-600 {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity, 1));
}

.text-stone-800 {
  --tw-text-opacity: 1;
  color: rgb(41 37 36 / var(--tw-text-opacity, 1));
}

.text-success {
  --tw-text-opacity: 1;
  color: rgb(20 164 77 / var(--tw-text-opacity, 1));
}

.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}

.text-transparent {
  color: transparent;
}

.text-violet-600 {
  --tw-text-opacity: 1;
  color: rgb(124 58 237 / var(--tw-text-opacity, 1));
}

.text-violet-800 {
  --tw-text-opacity: 1;
  color: rgb(91 33 182 / var(--tw-text-opacity, 1));
}

.text-warning {
  --tw-text-opacity: 1;
  color: rgb(228 161 27 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-200 {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.text-opacity-90 {
  --tw-text-opacity: 0.9;
}

.\!underline {
  text-decoration-line: underline !important;
}

.underline {
  text-decoration-line: underline;
}

.no-underline {
  text-decoration-line: none;
}

.underline-offset-auto {
  text-underline-offset: auto;
}

.placeholder-black::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(0 0 0 / var(--tw-placeholder-opacity, 1));
}

.placeholder-black::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(0 0 0 / var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.accent-\[\#4F46E5\] {
  accent-color: #4F46E5;
}

.accent-blue-600 {
  accent-color: #2563eb;
}

.\!opacity-0 {
  opacity: 0 !important;
}

.\!opacity-100 {
  opacity: 1 !important;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-80 {
  opacity: 0.8;
}

.opacity-90 {
  opacity: 0.9;
}

.opacity-\[\.53\] {
  opacity: .53;
}

.opacity-\[\.54\] {
  opacity: .54;
}

.\!shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0_10px_rgba\(0\2c 0\2c 0\2c 0\.10\)\] {
  --tw-shadow: 0 0 10px rgba(0,0,0,0.10);
  --tw-shadow-colored: 0 0 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0px_3px_0_rgba\(0\2c 0\2c 0\2c 0\.07\)\2c 0_2px_2px_0_rgba\(0\2c 0\2c 0\2c 0\.04\)\] {
  --tw-shadow: 0 0px 3px 0 rgba(0,0,0,0.07),0 2px 2px 0 rgba(0,0,0,0.04);
  --tw-shadow-colored: 0 0px 3px 0 var(--tw-shadow-color), 0 2px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_10px_15px_-3px_rgba\(0\2c 0\2c 0\2c 0\.07\)\2c 0_4px_6px_-2px_rgba\(0\2c 0\2c 0\2c 0\.05\)\] {
  --tw-shadow: 0 10px 15px -3px rgba(0,0,0,0.07),0 4px 6px -2px rgba(0,0,0,0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_2px_5px_0_rgba\(0\2c 0\2c 0\2c 0\.16\)\2c _0_2px_10px_0_rgba\(0\2c 0\2c 0\2c 0\.12\)\] {
  --tw-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
  --tw-shadow-colored: 0 2px 5px 0 var(--tw-shadow-color), 0 2px 10px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_4px_9px_-4px_\#3b71ca\] {
  --tw-shadow: 0 4px 9px -4px #3b71ca;
  --tw-shadow-colored: 0 4px 9px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_2px_15px_-3px_rgba\(0\2c 0\2c 0\2c \.07\)\2c _0px_10px_20px_-2px_rgba\(0\2c 0\2c 0\2c \.04\)\] {
  --tw-shadow: 0px 2px 15px -3px rgba(0,0,0,.07), 0px 10px 20px -2px rgba(0,0,0,.04);
  --tw-shadow-colored: 0px 2px 15px -3px var(--tw-shadow-color), 0px 10px 20px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.\!shadow-\[\#e25679\] {
  --tw-shadow-color: #e25679 !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-green-500 {
  --tw-shadow-color: #22c55e !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-red-500 {
  --tw-shadow-color: #ef4444 !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.shadow-indigo-600 {
  --tw-shadow-color: #4f46e5;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-primary {
  --tw-shadow-color: #4F46E5;
  --tw-shadow: var(--tw-shadow-colored);
}

.\!outline-none {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.outline-0 {
  outline-width: 0px;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-\[\#1f1d1a\] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 29 26 / var(--tw-ring-opacity, 1));
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}

.ring-blue-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity, 1));
}

.ring-indigo-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(79 70 229 / var(--tw-ring-opacity, 1));
}

.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}

.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[background-color\2c _opacity\] {
  transition-property: background-color, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[background-color\2c box-shadow\2c border\] {
  transition-property: background-color,box-shadow,border;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[height\] {
  transition-property: height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[opacity\] {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[transform\2c _opacity\] {
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[transform\2c height\] {
  transition-property: transform,height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[width\] {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.delay-\[0ms\] {
  transition-delay: 0ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-75 {
  transition-duration: 75ms;
}

.duration-\[1000ms\] {
  transition-duration: 1000ms;
}

.duration-\[150ms\] {
  transition-duration: 150ms;
}

.duration-\[200ms\] {
  transition-duration: 200ms;
}

.duration-\[250ms\] {
  transition-duration: 250ms;
}

.duration-\[350ms\] {
  transition-duration: 350ms;
}

.duration-\[400ms\] {
  transition-duration: 400ms;
}

.ease-\[cubic-bezier\(0\2c 0\2c 0\.15\2c 1\)\2c _cubic-bezier\(0\2c 0\2c 0\.15\2c 1\)\] {
  transition-timing-function: cubic-bezier(0,0,0.15,1), cubic-bezier(0,0,0.15,1);
}

.ease-\[cubic-bezier\(0\.25\2c 0\.1\2c 0\.25\2c 1\)\] {
  transition-timing-function: cubic-bezier(0.25,0.1,0.25,1);
}

.ease-\[cubic-bezier\(0\.25\2c 0\.1\2c 0\.25\2c 1\.0\)\] {
  transition-timing-function: cubic-bezier(0.25,0.1,0.25,1.0);
}

.ease-\[cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)\] {
  transition-timing-function: cubic-bezier(0.4,0,0.2,1);
}

.ease-\[ease\] {
  transition-timing-function: ease;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.will-change-transform {
  will-change: transform;
}

.\!\[clip\:rect\(0\2c 0\2c 0\2c 0\)\] {
  clip: rect(0,0,0,0) !important;
}

.\[clip\:rect\(0\2c 0\2c 0\2c 0\)\] {
  clip: rect(0,0,0,0);
}

.\[direction\:ltr\] {
  direction: ltr;
}

.\[overflow-anchor\:none\] {
  overflow-anchor: none;
}

.\[transition\:background-color_\.2s_linear\2c _height_\.2s_ease-in-out\] {
  transition: background-color .2s linear, height .2s ease-in-out;
}

.\[transition\:background-color_\.2s_linear\2c _width_\.2s_ease-in-out\2c _opacity\] {
  transition: background-color .2s linear, width .2s ease-in-out, opacity;
}

.\[transition\:background-color_250ms_cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)_0ms\2c box-shadow_250ms_cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)_0ms\2c border_250ms_cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)_0ms\] {
  transition: background-color 250ms cubic-bezier(0.4,0,0.2,1) 0ms,box-shadow 250ms cubic-bezier(0.4,0,0.2,1) 0ms,border 250ms cubic-bezier(0.4,0,0.2,1) 0ms;
}

/* Core Sans CR font faces - using the actual font files from StackEasy */

@font-face {
  font-family: 'Core Sans CR';

  src: url('/fonts/CRC25.otf') format('opentype');

  font-weight: 200;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Core Sans CR';

  src: url('/fonts/CRC35.otf') format('opentype');

  font-weight: 300;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Core Sans CR';

  src: url('/fonts/CRC55.otf') format('opentype');

  font-weight: 400;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Core Sans CR';

  src: url('/fonts/CRC65.otf') format('opentype');

  font-weight: 700;

  font-style: normal;

  font-display: swap;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: all 0.5s ease-in-out;
}

html {
  font-size: 0.875rem;
}

::-webkit-scrollbar {
  width: 0.625rem;
  border-radius: 0.75rem;
}

::-webkit-scrollbar-thumb {
  background-color: #a8a8a8;
  border-radius: 0.75rem;
}

body {
  position: relative;
  margin: 0;
  font-family: "Core Sans CR", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family:
    source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}

.react-toggle {
  touch-action: pan-x;
  display: inline-block;
  position: relative;
  cursor: pointer;
  background-color: transparent;
  border: 0;
  padding: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
}

.react-toggle-screenreader-only {
  border: 0;
  clip: rect(0 0 0 0);
  height: 0.0625rem;
  margin: -0.0625rem;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 0.0625rem;
}

.react-toggle--disabled {
  cursor: not-allowed;
  opacity: 0.5;
  transition: opacity 0.25s;
}

.react-toggle-track {
  width: 3.125rem;
  height: 1.5rem;
  padding: 0;
  border-radius: 1.875rem;
  background-color: #e4f1f7;
  transition: all 0.2s ease;
}

.react-toggle:hover:not(.react-toggle--disabled) .react-toggle-track {
  background-color: #eeeeee;
}

.react-toggle--checked .react-toggle-track,
.react-toggle--checked .react-toggle-track:hover {
  background-color: #4f46e5;
}

.react-toggle--checked:hover:not(.react-toggle--disabled) .react-toggle-track {
  background-color: #4f46e5;
}

.react-toggle-track-check {
  position: absolute;
  width: 0.875rem;
  height: 0.625rem;
  top: 0rem;
  bottom: 0rem;
  margin-top: auto;
  margin-bottom: auto;
  line-height: 0;
  left: 0.5rem;
  opacity: 0;
  transition: opacity 0.25s ease;
}

.react-toggle--checked .react-toggle-track-check {
  opacity: 1;
  transition: opacity 0.25s ease;
}

.react-toggle-track-x {
  position: absolute;
  width: 0.625rem;
  height: 0.625rem;
  top: 0rem;
  bottom: 0rem;
  margin-top: auto;
  margin-bottom: auto;
  line-height: 0;
  right: 0.625rem;
  opacity: 1;
  transition: opacity 0.25s ease;
}

.react-toggle--checked .react-toggle-track-x {
  opacity: 0;
}

.react-toggle-thumb {
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1) 0ms;
  position: absolute;
  top: 0.0625rem;
  left: 0.0625rem;
  width: 1.375rem;
  height: 1.375rem;
  border: 0.0625rem solid #fafafa;
  border-radius: 50%;
  background-color: #fafafa;
  box-sizing: border-box;
  transition: all 0.25s ease;
}

.react-toggle--checked .react-toggle-thumb {
  left: 1.6875rem;
  border-color: #4f46e5;
}

.react-toggle--focus .react-toggle-thumb {
  box-shadow: 0rem 0rem 0.125rem 0.1875rem #4f46e5;
}

.react-toggle:active:not(.react-toggle--disabled) .react-toggle-thumb {
  box-shadow: 0rem 0rem 0.3125rem 0.3125rem #4f46e5;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.custom-tooltip {
  max-width: 200px;
  /* Set a fixed width */
  white-space: pre-wrap;
  /* Allow text to wrap to the next line */
  word-wrap: break-word;
  /* Break long words */
}

.sidebar-holder {
  width: 100%;
  min-width: 15rem;
  max-width: 15rem;
  position: relative;
  background: #151515;
  color: #fff;
  z-index: 2;
  /* transition: all 0.3s; */
  min-height: 100vh;
  overflow: hidden;
  transition: 0.2s;
}

.open-nav {
  min-width: 0rem !important;
  max-width: 0rem !important;
  width: 0 !important;
  transition: 0.2s;
  opacity: 0;
}

.sidebar-list ul li a {
  padding: 0.625rem;
  display: block;
  width: 100%;
  font-size: 0.875rem;
  font-weight: 600;
  transition: 0.2s ease-in;
  text-transform: capitalize;
}

.sidebar-list .active-nav {
  padding: 0.75rem;
  color: #262626;
  border-radius: 0.375rem;
  background: #f4f4f4;
}

.sidebar-list .active-nav:hover {
  background: #f4f4f4;
}

.sidebar-list ul li a:hover {
  color: #262626;
}

.page-header {
  width: 100%;
  padding: 1.25rem;
  background: white;
}

.page-header span {
  cursor: pointer;
  display: block;
  width: -moz-fit-content;
  width: fit-content;
  font-size: 1.25rem;
}

.center-svg {
  aspect-ratio: 1/1;
  align-items: center;
  justify-content: center;
  line-height: 1.2em !important;
}

.uppy-Dashboard-inner {
  width: 100% !important;
}

@media screen and (max-width: 47.9375rem) {
  .sidebar-holder {
    width: 100%;
    min-width: 12.5rem;
    max-width: 12.5rem;
    position: fixed;
    top: 0;
    left: 0;
  }

  .page-header span {
    margin-left: auto;
  }
}

/* Custom slider styles */

.custom-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #2563eb;
  /* Blue-600 */
  border-radius: 50%;
  cursor: pointer;
  margin-top: -6px;
  /* Adjust to center thumb on track */
}

.custom-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #2563eb;
  /* Blue-600 */
  border-radius: 50%;
  cursor: pointer;
  border: none;
  /* FF adds a border by default */
}

/* Ensure the track has a defined height and appearance for consistency */

.custom-slider {
  height: 4px;
  /* Or same as h-2 in Tailwind */
  background-color: #e5e7eb;
  /* gray-200 */
  border-radius: 9999px;
  /* rounded-lg */
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  width: 100%;
}

.custom-slider:focus {
  outline: none;
  /* Optional: remove focus outline if not desired */
}

@media (min-width: 768px) {
  .md\:container {
    width: 100%;
  }

  @media (min-width: 320px) {
    .md\:container {
      max-width: 320px;
    }
  }

  @media (min-width: 640px) {
    .md\:container {
      max-width: 640px;
    }
  }

  @media (min-width: 768px) {
    .md\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .md\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .md\:container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {
    .md\:container {
      max-width: 1536px;
    }
  }
}

.selection\:bg-transparent *::-moz-selection {
  background-color: transparent;
}

.selection\:bg-transparent *::selection {
  background-color: transparent;
}

.selection\:bg-transparent::-moz-selection {
  background-color: transparent;
}

.selection\:bg-transparent::selection {
  background-color: transparent;
}

.placeholder\:text-left::-moz-placeholder {
  text-align: left;
}

.placeholder\:text-left::placeholder {
  text-align: left;
}

.backdrop\:blur-md::backdrop {
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.before\:pointer-events-none::before {
  content: var(--tw-content);
  pointer-events: none;
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:h-\[0\.875rem\]::before {
  content: var(--tw-content);
  height: 0.875rem;
}

.before\:w-\[0\.875rem\]::before {
  content: var(--tw-content);
  width: 0.875rem;
}

.before\:scale-0::before {
  content: var(--tw-content);
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:bg-transparent::before {
  content: var(--tw-content);
  background-color: transparent;
}

.before\:opacity-0::before {
  content: var(--tw-content);
  opacity: 0;
}

.before\:shadow-\[0px_0px_0px_13px_transparent\]::before {
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px transparent;
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}

.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}

.after\:\!ml-\[-1\.25rem\]::after {
  content: var(--tw-content);
  margin-left: -1.25rem !important;
}

.after\:mt-\[-120\%\]::after {
  content: var(--tw-content);
  margin-top: -120%;
}

.after\:block::after {
  content: var(--tw-content);
  display: block;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.after\:bg-\[\#90EE90\]::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(144 238 144 / var(--tw-bg-opacity, 1));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.after\:pl-\[350\%\]::after {
  content: var(--tw-content);
  padding-left: 350%;
}

.after\:pt-\[300\%\]::after {
  content: var(--tw-content);
  padding-top: 300%;
}

.after\:opacity-0::after {
  content: var(--tw-content);
  opacity: 0;
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.last\:mb-2:last-child {
  margin-bottom: 0.5rem;
}

.checked\:\!border-\[\#14a44d\]:checked {
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1)) !important;
}

.checked\:\!border-\[\#dc4c64\]:checked {
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1)) !important;
}

.checked\:border-primary:checked {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.checked\:\!bg-\[\#14a44d\]:checked {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(20 164 77 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:\!bg-\[\#dc4c64\]:checked {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 76 100 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:bg-primary:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.checked\:outline-none:checked {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.checked\:before\:opacity-\[0\.16\]:checked::before {
  content: var(--tw-content);
  opacity: 0.16;
}

.checked\:after\:absolute:checked::after {
  content: var(--tw-content);
  position: absolute;
}

.checked\:after\:-mt-px:checked::after {
  content: var(--tw-content);
  margin-top: -1px;
}

.checked\:after\:ml-\[0\.25rem\]:checked::after {
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.checked\:after\:block:checked::after {
  content: var(--tw-content);
  display: block;
}

.checked\:after\:h-\[0\.8125rem\]:checked::after {
  content: var(--tw-content);
  height: 0.8125rem;
}

.checked\:after\:w-\[0\.375rem\]:checked::after {
  content: var(--tw-content);
  width: 0.375rem;
}

.checked\:after\:rotate-45:checked::after {
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:after\:border-\[0\.125rem\]:checked::after {
  content: var(--tw-content);
  border-width: 0.125rem;
}

.checked\:after\:border-l-0:checked::after {
  content: var(--tw-content);
  border-left-width: 0px;
}

.checked\:after\:border-t-0:checked::after {
  content: var(--tw-content);
  border-top-width: 0px;
}

.checked\:after\:border-solid:checked::after {
  content: var(--tw-content);
  border-style: solid;
}

.checked\:after\:border-white:checked::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.checked\:after\:\!bg-\[\#14a44d\]:checked::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1 !important;
  background-color: rgb(20 164 77 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:after\:\!bg-\[\#dc4c64\]:checked::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 76 100 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:after\:bg-transparent:checked::after {
  content: var(--tw-content);
  background-color: transparent;
}

.checked\:after\:content-\[\'\'\]:checked::after {
  --tw-content: '';
  content: var(--tw-content);
}

.empty\:hidden:empty {
  display: none;
}

.focus-within\:border-gray-400:focus-within {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:top-\[80\%\]:hover {
  top: 80%;
}

.hover\:block:hover {
  display: block;
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-95:hover {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:rounded-\[50\%\]:hover {
  border-radius: 50%;
}

.hover\:border:hover {
  border-width: 1px;
}

.hover\:border-\[\#e25679\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(226 86 121 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:border-green-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}

.hover\:\!bg-\[\#eee\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1)) !important;
}

.hover\:bg-\[\#00000014\]:hover {
  background-color: #00000014;
}

.hover\:bg-\[\#00000026\]:hover {
  background-color: #00000026;
}

.hover\:bg-\[\#238e4e\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(35 142 78 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#2A2B32\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(42 43 50 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#F4F4F4\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#e25679\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(226 86 121 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[unset\]:hover {
  background-color: unset;
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500\/10:hover {
  background-color: rgb(107 114 128 / 0.1);
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-light:hover {
  background-color: #4F46E550;
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-20:hover {
  --tw-bg-opacity: 0.2;
}

.hover\:fill-\[\#8b8b8b\]:hover {
  fill: #8b8b8b;
}

.hover\:pr-4:hover {
  padding-right: 1rem;
}

.hover\:text-\[\#3b71ca\]:hover {
  --tw-text-opacity: 1;
  color: rgb(59 113 202 / var(--tw-text-opacity, 1));
}

.hover\:text-\[\#8b8b8b\]:hover {
  --tw-text-opacity: 1;
  color: rgb(139 139 139 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-600:hover {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-700:hover {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:\!opacity-90:hover {
  opacity: 0.9 !important;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:\!shadow-none:hover {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.hover\:shadow:hover {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.3\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.2\)\]:hover {
  --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.3),0 4px 18px 0 rgba(59,113,202,0.2);
  --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[\#e25679\]:hover {
  --tw-shadow-color: #e25679;
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:outline-none:hover {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.hover\:ease-in-out:hover {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.hover\:before\:opacity-\[0\.04\]:hover::before {
  content: var(--tw-content);
  opacity: 0.04;
}

.hover\:before\:shadow-\[0px_0px_0px_13px_rgba\(0\2c 0\2c 0\2c 0\.6\)\]:hover::before {
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px rgba(0,0,0,0.6);
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:top-\[80\%\]:focus {
  top: 80%;
}

.focus\:block:focus {
  display: block;
}

.focus\:rounded-\[50\%\]:focus {
  border-radius: 50%;
}

.focus\:\!border-\[\#14a44d\]:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1)) !important;
}

.focus\:\!border-\[\#dc4c64\]:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1)) !important;
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-gray-400:focus {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.focus\:border-primary:focus {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.focus\:\!bg-\[\#eee\]:focus {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1)) !important;
}

.focus\:bg-\[\#00000014\]:focus {
  background-color: #00000014;
}

.focus\:bg-\[\#00000026\]:focus {
  background-color: #00000026;
}

.focus\:bg-blue-700:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.focus\:bg-neutral-200:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.focus\:text-\[\#3b71ca\]:focus {
  --tw-text-opacity: 1;
  color: rgb(59 113 202 / var(--tw-text-opacity, 1));
}

.focus\:text-gray-700:focus {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.focus\:text-primary:focus {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.focus\:text-white:focus {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\:\!opacity-90:focus {
  opacity: 0.9 !important;
}

.focus\:opacity-100:focus {
  opacity: 1;
}

.focus\:\!shadow-\[inset_0_0_0_1px_\#14a44d\]:focus {
  --tw-shadow: inset 0 0 0 1px #14a44d !important;
  --tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus\:\!shadow-\[inset_0_0_0_1px_\#dc4c64\]:focus {
  --tw-shadow: inset 0 0 0 1px #dc4c64 !important;
  --tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.3\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.2\)\]:focus {
  --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.3),0 4px 18px 0 rgba(59,113,202,0.2);
  --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-lg:focus {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-none:focus {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-te-primary:focus {
  --tw-shadow: 0 0 0 1px rgb(59, 113, 202);
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline-0:focus {
  outline-width: 0px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-inset:focus {
  --tw-ring-inset: inset;
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(79 70 229 / var(--tw-ring-opacity, 1));
}

.focus\:ring-transparent:focus {
  --tw-ring-color: transparent;
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-800:focus {
  --tw-ring-offset-color: #1f2937;
}

.focus\:transition-\[border-color_0\.2s\]:focus {
  transition-property: border-color 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.focus\:placeholder\:opacity-100:focus::-moz-placeholder {
  opacity: 1;
}

.focus\:placeholder\:opacity-100:focus::placeholder {
  opacity: 1;
}

.focus\:before\:scale-100:focus::before {
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus\:before\:opacity-\[0\.12\]:focus::before {
  content: var(--tw-content);
  opacity: 0.12;
}

.focus\:before\:shadow-\[0px_0px_0px_13px_rgba\(0\2c 0\2c 0\2c 0\.6\)\]:focus::before {
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px rgba(0,0,0,0.6);
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:before\:transition-\[box-shadow_0\.2s\2c transform_0\.2s\]:focus::before {
  content: var(--tw-content);
  transition-property: box-shadow 0.2s,transform 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.focus\:after\:absolute:focus::after {
  content: var(--tw-content);
  position: absolute;
}

.focus\:after\:z-\[1\]:focus::after {
  content: var(--tw-content);
  z-index: 1;
}

.focus\:after\:block:focus::after {
  content: var(--tw-content);
  display: block;
}

.focus\:after\:h-\[0\.875rem\]:focus::after {
  content: var(--tw-content);
  height: 0.875rem;
}

.focus\:after\:w-\[0\.875rem\]:focus::after {
  content: var(--tw-content);
  width: 0.875rem;
}

.focus\:after\:rounded-\[0\.125rem\]:focus::after {
  content: var(--tw-content);
  border-radius: 0.125rem;
}

.focus\:after\:content-\[\'\'\]:focus::after {
  --tw-content: '';
  content: var(--tw-content);
}

.checked\:focus\:before\:scale-100:focus:checked::before {
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:focus\:before\:shadow-\[0px_0px_0px_13px_\#3b71ca\]:focus:checked::before {
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px #3b71ca;
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.checked\:focus\:before\:transition-\[box-shadow_0\.2s\2c transform_0\.2s\]:focus:checked::before {
  content: var(--tw-content);
  transition-property: box-shadow 0.2s,transform 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.checked\:focus\:after\:-mt-px:focus:checked::after {
  content: var(--tw-content);
  margin-top: -1px;
}

.checked\:focus\:after\:ml-\[0\.25rem\]:focus:checked::after {
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.checked\:focus\:after\:h-\[0\.8125rem\]:focus:checked::after {
  content: var(--tw-content);
  height: 0.8125rem;
}

.checked\:focus\:after\:w-\[0\.375rem\]:focus:checked::after {
  content: var(--tw-content);
  width: 0.375rem;
}

.checked\:focus\:after\:rotate-45:focus:checked::after {
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:focus\:after\:rounded-none:focus:checked::after {
  content: var(--tw-content);
  border-radius: 0px;
}

.checked\:focus\:after\:border-\[0\.125rem\]:focus:checked::after {
  content: var(--tw-content);
  border-width: 0.125rem;
}

.checked\:focus\:after\:border-l-0:focus:checked::after {
  content: var(--tw-content);
  border-left-width: 0px;
}

.checked\:focus\:after\:border-t-0:focus:checked::after {
  content: var(--tw-content);
  border-top-width: 0px;
}

.checked\:focus\:after\:border-solid:focus:checked::after {
  content: var(--tw-content);
  border-style: solid;
}

.checked\:focus\:after\:border-white:focus:checked::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.checked\:focus\:after\:bg-transparent:focus:checked::after {
  content: var(--tw-content);
  background-color: transparent;
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-blue-500:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.active\:bg-\[\#c4d4ef\]:active {
  --tw-bg-opacity: 1;
  background-color: rgb(196 212 239 / var(--tw-bg-opacity, 1));
}

.active\:bg-\[\#cacfd1\]:active {
  --tw-bg-opacity: 1;
  background-color: rgb(202 207 209 / var(--tw-bg-opacity, 1));
}

.active\:bg-blue-800:active {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.active\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.3\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.2\)\]:active {
  --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.3),0 4px 18px 0 rgba(59,113,202,0.2);
  --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.active\:shadow-lg:active {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.active\:after\:m-0:active::after {
  content: var(--tw-content);
  margin: 0px;
}

.active\:after\:p-0:active::after {
  content: var(--tw-content);
  padding: 0px;
}

.active\:after\:opacity-100:active::after {
  content: var(--tw-content);
  opacity: 1;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-blue-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-500:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.disabled\:text-gray-400:disabled {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.disabled\:text-slate-300:disabled {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.disabled\:opacity-40:disabled {
  opacity: 0.4;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:hover\:bg-transparent:hover:disabled {
  background-color: transparent;
}

.group:hover .group-hover\:block {
  display: block;
}

.group\/x:hover .group-hover\/x\:h-\[11px\] {
  height: 11px;
}

.group\/y:hover .group-hover\/y\:w-\[11px\] {
  width: 11px;
}

.group\/x:hover .group-hover\/x\:bg-\[\#999\] {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/y:hover .group-hover\/y\:bg-\[\#999\] {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:from-\[\#2A2B32\] {
  --tw-gradient-from: #2A2B32 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 43 50 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.group:hover .group-hover\:fill-\[\#ef4444\] {
  fill: #ef4444;
}

.group:hover .group-hover\:stroke-\[\#ef4444\] {
  stroke: #ef4444;
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.group\/ps:hover .group-hover\/ps\:opacity-60 {
  opacity: 0.6;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group\/x:focus .group-focus\/x\:h-\[0\.6875rem\] {
  height: 0.6875rem;
}

.group\/y:focus .group-focus\/y\:w-\[0\.6875rem\] {
  width: 0.6875rem;
}

.group\/x:focus .group-focus\/x\:bg-\[\#999\] {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/y:focus .group-focus\/y\:bg-\[\#999\] {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/ps:focus .group-focus\/ps\:opacity-100 {
  opacity: 1;
}

.group\/ps:focus .group-focus\/ps\:opacity-60 {
  opacity: 0.6;
}

.group\/ps:active .group-active\/ps\:opacity-100 {
  opacity: 1;
}

.group\/ps.ps--active-x .group-\[\&\.ps--active-x\]\/ps\:block {
  display: block;
}

.group\/ps.ps--active-y .group-\[\&\.ps--active-y\]\/ps\:block {
  display: block;
}

.group\/x.ps--clicking .group-\[\&\.ps--clicking\]\/x\:h-\[11px\] {
  height: 11px;
}

.group\/y.ps--clicking .group-\[\&\.ps--clicking\]\/y\:w-\[11px\] {
  width: 11px;
}

.group[data-te-datepicker-cell-current] .group-\[\[data-te-datepicker-cell-current\]\]\:border {
  border-width: 1px;
}

.group[data-te-datepicker-cell-current] .group-\[\[data-te-datepicker-cell-current\]\]\:border-solid {
  border-style: solid;
}

.group[data-te-datepicker-cell-current] .group-\[\[data-te-datepicker-cell-current\]\]\:border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.group\/ps.ps--active-x .group-\[\&\.ps--active-x\]\/ps\:bg-transparent {
  background-color: transparent;
}

.group\/ps.ps--active-y .group-\[\&\.ps--active-y\]\/ps\:bg-transparent {
  background-color: transparent;
}

.group\/x.ps--clicking .group-\[\&\.ps--clicking\]\/x\:bg-\[\#999\] {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/y.ps--clicking .group-\[\&\.ps--clicking\]\/y\:bg-\[\#999\] {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group:not([data-te-datepicker-cell-disabled]):not([data-te-datepicker-cell-selected]):hover .group-\[\:not\(\[data-te-datepicker-cell-disabled\]\)\:not\(\[data-te-datepicker-cell-selected\]\)\:hover\]\:bg-neutral-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

.group:not([data-te-datepicker-cell-selected])[data-te-datepicker-cell-focused] .group-\[\:not\(\[data-te-datepicker-cell-selected\]\)\[data-te-datepicker-cell-focused\]\]\:bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.group[data-te-datepicker-cell-selected] .group-\[\[data-te-datepicker-cell-selected\]\]\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.group[data-te-datepicker-cell-selected] .group-\[\[data-te-datepicker-cell-selected\]\]\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group\/ps.ps--scrolling-x .group-\[\&\.ps--scrolling-x\]\/ps\:opacity-60 {
  opacity: 0.6;
}

.group\/ps.ps--scrolling-y .group-\[\&\.ps--scrolling-y\]\/ps\:opacity-60 {
  opacity: 0.6;
}

.peer:checked ~ .peer-checked\:bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:top-\[80\%\] {
  top: 80%;
}

.peer:focus ~ .peer-focus\:block {
  display: block;
}

.peer:focus ~ .peer-focus\:-translate-y-\[0\.75rem\] {
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:-translate-y-\[0\.9rem\] {
  --tw-translate-y: -0.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:-translate-y-\[1\.15rem\] {
  --tw-translate-y: -1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:scale-\[0\.8\] {
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:opacity-100 {
  opacity: 1;
}

.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus-visible ~ .peer-focus-visible\:top-\[80\%\] {
  top: 80%;
}

.peer:focus-visible ~ .peer-focus-visible\:block {
  display: block;
}

.peer:focus-visible ~ .peer-focus-visible\:opacity-100 {
  opacity: 1;
}

.data-\[te-datepicker-cell-disabled\]\:pointer-events-none[data-te-datepicker-cell-disabled] {
  pointer-events: none;
}

.data-\[te-active\]\:-top-\[38px\][data-te-active] {
  top: -38px;
}

.data-\[te-carousel-fade\]\:z-0[data-te-carousel-fade] {
  z-index: 0;
}

.data-\[te-carousel-fade\]\:z-\[1\][data-te-carousel-fade] {
  z-index: 1;
}

.data-\[te-input-state-active\]\:block[data-te-input-state-active] {
  display: block;
}

.data-\[popper-reference-hidden\]\:hidden[data-popper-reference-hidden] {
  display: none;
}

.data-\[te-input-state-active\]\:-translate-y-\[0\.75rem\][data-te-input-state-active] {
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-input-state-active\]\:-translate-y-\[0\.9rem\][data-te-input-state-active] {
  --tw-translate-y: -0.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-input-state-active\]\:-translate-y-\[1\.15rem\][data-te-input-state-active] {
  --tw-translate-y: -1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-active\]\:scale-100[data-te-active] {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-input-state-active\]\:scale-\[0\.8\][data-te-input-state-active] {
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-select-open\]\:scale-100[data-te-select-open] {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-autocomplete-state-open\]\:scale-y-100[data-te-autocomplete-state-open] {
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[data-te-autocomplete-option-disabled\]\:cursor-default[data-data-te-autocomplete-option-disabled] {
  cursor: default;
}

.data-\[te-datepicker-cell-disabled\]\:cursor-default[data-te-datepicker-cell-disabled] {
  cursor: default;
}

.data-\[te-input-disabled\]\:cursor-default[data-te-input-disabled] {
  cursor: default;
}

.data-\[te-select-option-disabled\]\:cursor-default[data-te-select-option-disabled] {
  cursor: default;
}

.data-\[te-select-selected\]\:data-\[te-select-option-disabled\]\:cursor-default[data-te-select-option-disabled][data-te-select-selected] {
  cursor: default;
}

.data-\[te-autocomplete-item-active\]\:bg-black\/5[data-te-autocomplete-item-active] {
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-input-disabled\]\:bg-\[\#e9ecef\][data-te-input-disabled] {
  --tw-bg-opacity: 1;
  background-color: rgb(233 236 239 / var(--tw-bg-opacity, 1));
}

.data-\[te-input-multiple-active\]\:bg-black\/5[data-te-input-multiple-active] {
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-input-state-active\]\:bg-black\/5[data-te-input-state-active] {
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-select-option-selected\]\:bg-black\/\[0\.02\][data-te-select-option-selected] {
  background-color: rgb(0 0 0 / 0.02);
}

.data-\[te-select-option-selected\]\:data-\[te-input-state-active\]\:bg-black\/5[data-te-input-state-active][data-te-select-option-selected] {
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-select-selected\]\:data-\[te-select-option-disabled\]\:bg-transparent[data-te-select-option-disabled][data-te-select-selected] {
  background-color: transparent;
}

.data-\[data-te-autocomplete-option-disabled\]\:text-gray-400[data-data-te-autocomplete-option-disabled] {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-\[te-datepicker-cell-disabled\]\:text-neutral-300[data-te-datepicker-cell-disabled] {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}

.data-\[te-select-option-disabled\]\:text-gray-400[data-te-select-option-disabled] {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-\[te-select-selected\]\:data-\[te-select-option-disabled\]\:text-gray-400[data-te-select-option-disabled][data-te-select-selected] {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-\[te-autocomplete-state-open\]\:opacity-100[data-te-autocomplete-state-open] {
  opacity: 1;
}

.data-\[te-carousel-fade\]\:opacity-0[data-te-carousel-fade] {
  opacity: 0;
}

.data-\[te-carousel-fade\]\:opacity-100[data-te-carousel-fade] {
  opacity: 1;
}

.data-\[te-select-open\]\:opacity-100[data-te-select-open] {
  opacity: 1;
}

.data-\[te-carousel-fade\]\:duration-\[600ms\][data-te-carousel-fade] {
  transition-duration: 600ms;
}

.data-\[te-input-state-active\]\:placeholder\:opacity-100[data-te-input-state-active]::-moz-placeholder {
  opacity: 1;
}

.data-\[te-input-state-active\]\:placeholder\:opacity-100[data-te-input-state-active]::placeholder {
  opacity: 1;
}

.data-\[te-datepicker-cell-disabled\]\:hover\:cursor-default:hover[data-te-datepicker-cell-disabled] {
  cursor: default;
}

.group[data-te-datepicker-cell-focused] .group-\[\[data-te-datepicker-cell-focused\]\]\:data-\[te-datepicker-cell-selected\]\:bg-primary[data-te-datepicker-cell-selected] {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.group\/validation[data-te-was-validated] .group-data-\[te-was-validated\]\/validation\:mb-4 {
  margin-bottom: 1rem;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-x-0 {
  border-left-width: 0px;
  border-right-width: 0px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-x-0 {
  border-left-width: 0px;
  border-right-width: 0px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-l-0 {
  border-left-width: 0px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-r-0 {
  border-right-width: 0px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-t {
  border-top-width: 1px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-l-0 {
  border-left-width: 0px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-r-0 {
  border-right-width: 0px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-t {
  border-top-width: 1px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-solid {
  border-style: solid;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-solid {
  border-style: solid;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-\[\#14a44d\] {
  --tw-border-opacity: 1;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-\[\#dc4c64\] {
  --tw-border-opacity: 1;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-t-transparent {
  border-top-color: transparent;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-t-transparent {
  border-top-color: transparent;
}

.group\/opt[data-te-select-option-group-ref] .group-data-\[te-select-option-group-ref\]\/opt\:pl-7 {
  padding-left: 1.75rem;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#14a44d\2c _0_1px_0_0_\#14a44d\2c _0_-1px_0_0_\#14a44d\] {
  --tw-shadow: -1px 0 0 #14a44d, 0 1px 0 0 #14a44d, 0 -1px 0 0 #14a44d;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#3b71ca\2c _0_1px_0_0_\#3b71ca\2c _0_-1px_0_0_\#3b71ca\] {
  --tw-shadow: -1px 0 0 #3b71ca, 0 1px 0 0 #3b71ca, 0 -1px 0 0 #3b71ca;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#dc4c64\2c _0_1px_0_0_\#dc4c64\2c _0_-1px_0_0_\#dc4c64\] {
  --tw-shadow: -1px 0 0 #dc4c64, 0 1px 0 0 #dc4c64, 0 -1px 0 0 #dc4c64;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#ffffff\2c _0_1px_0_0_\#ffffff\2c _0_-1px_0_0_\#ffffff\] {
  --tw-shadow: -1px 0 0 #ffffff, 0 1px 0 0 #ffffff, 0 -1px 0 0 #ffffff;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#14a44d\] {
  --tw-shadow: 0 1px 0 0 #14a44d;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#3b71ca\] {
  --tw-shadow: 0 1px 0 0 #3b71ca;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#dc4c64\] {
  --tw-shadow: 0 1px 0 0 #dc4c64;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#ffffff\] {
  --tw-shadow: 0 1px 0 0 #ffffff;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#14a44d\2c _0_-1px_0_0_\#14a44d\2c _0_1px_0_0_\#14a44d\] {
  --tw-shadow: 1px 0 0 #14a44d, 0 -1px 0 0 #14a44d, 0 1px 0 0 #14a44d;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#3b71ca\2c _0_-1px_0_0_\#3b71ca\2c _0_1px_0_0_\#3b71ca\] {
  --tw-shadow: 1px 0 0 #3b71ca, 0 -1px 0 0 #3b71ca, 0 1px 0 0 #3b71ca;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#dc4c64\2c _0_-1px_0_0_\#dc4c64\2c _0_1px_0_0_\#dc4c64\] {
  --tw-shadow: 1px 0 0 #dc4c64, 0 -1px 0 0 #dc4c64, 0 1px 0 0 #dc4c64;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#ffffff\2c _0_-1px_0_0_\#ffffff\2c _0_1px_0_0_\#ffffff\] {
  --tw-shadow: 1px 0 0 #ffffff, 0 -1px 0 0 #ffffff, 0 1px 0 0 #ffffff;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group\/validation[data-te-was-validated] .peer:valid ~ .group-data-\[te-was-validated\]\/validation\:peer-valid\:block {
  display: block;
}

.group\/validation[data-te-was-validated] .peer:valid ~ .group-data-\[te-was-validated\]\/validation\:peer-valid\:text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.group\/validation[data-te-was-validated] .peer:invalid ~ .group-data-\[te-was-validated\]\/validation\:peer-invalid\:block {
  display: block;
}

.group\/validation[data-te-was-validated] .peer:invalid ~ .group-data-\[te-was-validated\]\/validation\:peer-invalid\:text-\[rgb\(220\2c 76\2c 100\)\] {
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:-translate-y-\[0\.75rem\] {
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:-translate-y-\[0\.9rem\] {
  --tw-translate-y: -0.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:-translate-y-\[1\.15rem\] {
  --tw-translate-y: -1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:scale-\[0\.8\] {
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-focused] ~ .peer-data-\[te-input-focused\]\:\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.peer[data-te-input-focused] ~ .peer-data-\[te-input-focused\]\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:transform-none {
    transform: none;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .motion-reduce\:animate-\[spin_1\.5s_linear_infinite\] {
    animation: spin 1.5s linear infinite;
  }

  @keyframes spinner-grow {
    0% {
      transform: scale(0);
    }

    50% {
      transform: none;
      opacity: 1;
    }
  }

  .motion-reduce\:animate-\[spinner-grow_1\.5s_linear_infinite\] {
    animation: spinner-grow 1.5s linear infinite;
  }

  .motion-reduce\:animate-none {
    animation: none;
  }

  .motion-reduce\:transition-none {
    transition-property: none;
  }
}

@media (min-width: 320px) {
  .xs\:mb-5 {
    margin-bottom: 1.25rem;
  }
}

@media (min-width: 640px) {
  .sm\:static {
    position: static;
  }

  .sm\:inset-auto {
    inset: auto;
  }

  .sm\:right-4 {
    right: 1rem;
  }

  .sm\:right-5 {
    right: 1.25rem;
  }

  .sm\:top-5 {
    top: 1.25rem;
  }

  .sm\:top-7 {
    top: 1.75rem;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-3 {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .sm\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .sm\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:mr-2 {
    margin-right: 0.5rem;
  }

  .sm\:mr-3 {
    margin-right: 0.75rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-10 {
    margin-top: 2.5rem;
  }

  .sm\:mt-2 {
    margin-top: 0.5rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-4 {
    margin-top: 1rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-\[700px\] {
    height: 700px;
  }

  .sm\:max-h-\[90vh\] {
    max-height: 90vh;
  }

  .sm\:w-64 {
    width: 16rem;
  }

  .sm\:w-\[250px\] {
    width: 250px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-\[80\%\] {
    max-width: 80%;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-wrap {
    flex-wrap: wrap;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:items-stretch {
    align-items: stretch;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:rounded-xl {
    border-radius: 0.75rem;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .sm\:py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .sm\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pl-3 {
    padding-left: 0.75rem;
  }

  .sm\:pl-6 {
    padding-left: 1.5rem;
  }

  .sm\:pr-0 {
    padding-right: 0px;
  }

  .sm\:pr-6 {
    padding-right: 1.5rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:\!text-base {
    font-size: 1rem !important;
    line-height: 1.5rem !important;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 768px) {
  .md\:relative {
    position: relative;
  }

  .md\:inset-0 {
    inset: 0px;
  }

  .md\:-right-\[170px\] {
    right: -170px;
  }

  .md\:right-2 {
    right: 0.5rem;
  }

  .md\:order-none {
    order: 0;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:m-auto {
    margin: auto;
  }

  .md\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .md\:mx-\[3rem\] {
    margin-left: 3rem;
    margin-right: 3rem;
  }

  .md\:mx-\[6rem\] {
    margin-left: 6rem;
    margin-right: 6rem;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .md\:ml-8 {
    margin-left: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-48 {
    height: 12rem;
  }

  .md\:h-52 {
    height: 13rem;
  }

  .md\:h-\[20px\] {
    height: 20px;
  }

  .md\:h-\[350px\] {
    height: 350px;
  }

  .md\:h-\[50\%\] {
    height: 50%;
  }

  .md\:h-\[70\%\] {
    height: 70%;
  }

  .md\:h-\[90\%\] {
    height: 90%;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:\!max-h-\[90\%\] {
    max-height: 90% !important;
  }

  .md\:max-h-\[50\%\] {
    max-height: 50%;
  }

  .md\:max-h-\[70\%\] {
    max-height: 70%;
  }

  .md\:max-h-\[90\%\] {
    max-height: 90%;
  }

  .md\:min-h-\[50\%\] {
    min-height: 50%;
  }

  .md\:min-h-\[70\%\] {
    min-height: 70%;
  }

  .md\:min-h-\[90\%\] {
    min-height: 90%;
  }

  .md\:\!w-\[25\%\] {
    width: 25% !important;
  }

  .md\:\!w-\[25\.125rem\] {
    width: 25.125rem !important;
  }

  .md\:\!w-\[29\.0625rem\] {
    width: 29.0625rem !important;
  }

  .md\:\!w-\[30\.375rem\] {
    width: 30.375rem !important;
  }

  .md\:\!w-\[35\.375rem\] {
    width: 35.375rem !important;
  }

  .md\:\!w-\[75\%\] {
    width: 75% !important;
  }

  .md\:\!w-\[90\%\] {
    width: 90% !important;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/6 {
    width: 16.666667%;
  }

  .md\:w-5\/6 {
    width: 83.333333%;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:w-9\/12 {
    width: 75%;
  }

  .md\:w-\[18\.775rem\] {
    width: 18.775rem;
  }

  .md\:w-\[21px\] {
    width: 21px;
  }

  .md\:w-\[22\.8125rem\] {
    width: 22.8125rem;
  }

  .md\:w-\[23rem\] {
    width: 23rem;
  }

  .md\:w-\[260px\] {
    width: 260px;
  }

  .md\:w-\[300px\] {
    width: 300px;
  }

  .md\:w-\[30rem\] {
    width: 30rem;
  }

  .md\:w-\[38\.5625rem\] {
    width: 38.5625rem;
  }

  .md\:w-\[400px\] {
    width: 400px;
  }

  .md\:w-\[60\%\] {
    width: 60%;
  }

  .md\:w-\[70\%\] {
    width: 70%;
  }

  .md\:w-\[auto\] {
    width: auto;
  }

  .md\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:min-w-\[18\.75rem\] {
    min-width: 18.75rem;
  }

  .md\:min-w-\[23\.6875rem\] {
    min-width: 23.6875rem;
  }

  .md\:min-w-\[30rem\] {
    min-width: 30rem;
  }

  .md\:min-w-\[60\%\] {
    min-width: 60%;
  }

  .md\:min-w-full {
    min-width: 100%;
  }

  .md\:max-w-2xl {
    max-width: 42rem;
  }

  .md\:max-w-\[18\.75rem\] {
    max-width: 18.75rem;
  }

  .md\:max-w-\[30rem\] {
    max-width: 30rem;
  }

  .md\:max-w-fit {
    max-width: -moz-fit-content;
    max-width: fit-content;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:max-w-md {
    max-width: 28rem;
  }

  .md\:max-w-sm {
    max-width: 24rem;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-x-10 {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }

  .md\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .md\:gap-y-3 {
    row-gap: 0.75rem;
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:overflow-visible {
    overflow: visible;
  }

  .md\:overflow-x-clip {
    overflow-x: clip;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:border-t-0 {
    border-top-width: 0px;
  }

  .md\:border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  }

  .md\:border-transparent {
    border-color: transparent;
  }

  .md\:\!bg-transparent {
    background-color: transparent !important;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-5 {
    padding: 1.25rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:pb-4 {
    padding-bottom: 1rem;
  }

  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .md\:pr-1 {
    padding-right: 0.25rem;
  }

  .md\:pr-6 {
    padding-right: 1.5rem;
  }

  .md\:pr-\[17px\] {
    padding-right: 17px;
  }

  .md\:pt-3 {
    padding-top: 0.75rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .md\:last\:mb-6:last-child {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-80 {
    height: 20rem;
  }

  .lg\:max-h-\[60vh\] {
    max-height: 60vh;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-\[calc\(100\%-115px\)\] {
    width: calc(100% - 115px);
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:max-w-2xl {
    max-width: 42rem;
  }

  .lg\:max-w-xl {
    max-width: 36rem;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

@media (min-width: 1280px) {
  .xl\:flex {
    display: flex;
  }

  .xl\:h-6 {
    height: 1.5rem;
  }

  .xl\:w-6 {
    width: 1.5rem;
  }

  .xl\:max-w-3xl {
    max-width: 48rem;
  }

  .xl\:flex-col {
    flex-direction: column;
  }
}

@media (min-width: 320px) {
  @media not all and (min-width: 768px) {
    @media (orientation: landscape) {
      .xs\:max-md\:landscape\:mt-24 {
        margin-top: 6rem;
      }

      .xs\:max-md\:landscape\:h-8 {
        height: 2rem;
      }

      .xs\:max-md\:landscape\:h-\[360px\] {
        height: 360px;
      }

      .xs\:max-md\:landscape\:h-full {
        height: 100%;
      }

      .xs\:max-md\:landscape\:w-8 {
        width: 2rem;
      }

      .xs\:max-md\:landscape\:w-\[475px\] {
        width: 475px;
      }

      .xs\:max-md\:landscape\:flex-row {
        flex-direction: row;
      }
    }
  }

  @media (max-width: 825px) {
    @media (orientation: landscape) {
      .min-\[320px\]\:max-\[825px\]\:landscape\:h-auto {
        height: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:min-h-\[305px\] {
        min-height: 305px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:min-h-\[auto\] {
        min-height: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:min-w-\[auto\] {
        min-width: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:\!flex-row {
        flex-direction: row !important;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:flex-col {
        flex-direction: column;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:\!justify-around {
        justify-content: space-around !important;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:overflow-y-auto {
        overflow-y: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-lg {
        border-radius: 0.5rem;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-bl-lg {
        border-bottom-left-radius: 0.5rem;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-bl-none {
        border-bottom-left-radius: 0px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-tr-none {
        border-top-right-radius: 0px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:p-\[10px\] {
        padding: 10px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:pr-\[10px\] {
        padding-right: 10px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:text-5xl {
        font-size: 3rem;
        line-height: 1;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:text-\[3rem\] {
        font-size: 3rem;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:font-normal {
        font-weight: 400;
      }
    }
  }
}

.rtl\:\!left-auto:where([dir="rtl"], [dir="rtl"] *) {
  left: auto !important;
}

.rtl\:\!origin-\[50\%_50\%_0\]:where([dir="rtl"], [dir="rtl"] *) {
  transform-origin: 50% 50% 0 !important;
}

.rtl\:\[direction\:rtl\]:where([dir="rtl"], [dir="rtl"] *) {
  direction: rtl;
}

@media (prefers-color-scheme: dark) {
  .dark\:border-0 {
    border-width: 0px;
  }

  .dark\:border-\[\#14a44d\] {
    --tw-border-opacity: 1;
    border-color: rgb(20 164 77 / var(--tw-border-opacity, 1));
  }

  .dark\:border-\[\#4f4f4f\] {
    --tw-border-opacity: 1;
    border-color: rgb(79 79 79 / var(--tw-border-opacity, 1));
  }

  .dark\:border-\[\#dc4c64\] {
    --tw-border-opacity: 1;
    border-color: rgb(220 76 100 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-600 {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-900\/50 {
    border-color: rgb(17 24 39 / 0.5);
  }

  .dark\:border-neutral-400 {
    --tw-border-opacity: 1;
    border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
  }

  .dark\:border-neutral-500 {
    --tw-border-opacity: 1;
    border-color: rgb(115 115 115 / var(--tw-border-opacity, 1));
  }

  .dark\:border-neutral-600 {
    --tw-border-opacity: 1;
    border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));
  }

  .dark\:border-white\/20 {
    border-color: rgb(255 255 255 / 0.2);
  }

  .dark\:\!bg-neutral-600 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:bg-\[\#4f4f4f\] {
    --tw-bg-opacity: 1;
    background-color: rgb(79 79 79 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-neutral-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-neutral-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-neutral-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-transparent {
    background-color: transparent;
  }

  .dark\:bg-zinc-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-zinc-600\/50 {
    background-color: rgb(82 82 91 / 0.5);
  }

  .dark\:bg-zinc-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-zinc-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
  }

  .dark\:fill-gray-400 {
    fill: #9ca3af;
  }

  .dark\:text-gray-100 {
    --tw-text-opacity: 1;
    color: rgb(243 244 246 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-200 {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dark\:text-neutral-200 {
    --tw-text-opacity: 1;
    color: rgb(229 229 229 / var(--tw-text-opacity, 1));
  }

  .dark\:text-neutral-300 {
    --tw-text-opacity: 1;
    color: rgb(212 212 212 / var(--tw-text-opacity, 1));
  }

  .dark\:text-neutral-400 {
    --tw-text-opacity: 1;
    color: rgb(163 163 163 / var(--tw-text-opacity, 1));
  }

  .dark\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .dark\:text-white\/50 {
    color: rgb(255 255 255 / 0.5);
  }

  .dark\:placeholder-gray-400::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
  }

  .dark\:placeholder-gray-400::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
  }

  .dark\:shadow-\[0_0_15px_rgba\(0\2c 0\2c 0\2c 0\.10\)\] {
    --tw-shadow: 0 0 15px rgba(0,0,0,0.10);
    --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:shadow-\[0_4px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.5\)\] {
    --tw-shadow: 0 4px 9px -4px rgba(59,113,202,0.5);
    --tw-shadow-colored: 0 4px 9px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:placeholder\:text-gray-200::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-gray-200::placeholder {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:checked\:border-primary:checked {
    --tw-border-opacity: 1;
    border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
  }

  .dark\:checked\:bg-primary:checked {
    --tw-bg-opacity: 1;
    background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:\!bg-\[\#555\]:hover {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(85 85 85 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:hover\:bg-neutral-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(115 115 115 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-neutral-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-neutral-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-white\/10:hover {
    background-color: rgb(255 255 255 / 0.1);
  }

  .dark\:hover\:fill-gray-100:hover {
    fill: #f3f4f6;
  }

  .dark\:hover\:text-\[\#3b71ca\]:hover {
    --tw-text-opacity: 1;
    color: rgb(59 113 202 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.2\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.1\)\]:hover {
    --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.2),0 4px 18px 0 rgba(59,113,202,0.1);
    --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:focus\:border-blue-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
  }

  .dark\:focus\:\!bg-\[\#555\]:focus {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(85 85 85 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:focus\:bg-white\/10:focus {
    background-color: rgb(255 255 255 / 0.1);
  }

  .dark\:focus\:text-\[\#3b71ca\]:focus {
    --tw-text-opacity: 1;
    color: rgb(59 113 202 / var(--tw-text-opacity, 1));
  }

  .dark\:focus\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.2\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.1\)\]:focus {
    --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.2),0 4px 18px 0 rgba(59,113,202,0.1);
    --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
  }

  .dark\:focus\:before\:shadow-\[0px_0px_0px_13px_rgba\(255\2c 255\2c 255\2c 0\.4\)\]:focus::before {
    content: var(--tw-content);
    --tw-shadow: 0px 0px 0px 13px rgba(255,255,255,0.4);
    --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:checked\:focus\:before\:shadow-\[0px_0px_0px_13px_\#3b71ca\]:focus:checked::before {
    content: var(--tw-content);
    --tw-shadow: 0px 0px 0px 13px #3b71ca;
    --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:active\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.2\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.1\)\]:active {
    --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.2),0 4px 18px 0 rgba(59,113,202,0.1);
    --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:disabled\:text-neutral-600:disabled {
    --tw-text-opacity: 1;
    color: rgb(82 82 82 / var(--tw-text-opacity, 1));
  }

  .dark\:disabled\:hover\:bg-transparent:hover:disabled {
    background-color: transparent;
  }

  .group[data-te-datepicker-cell-current] .dark\:group-\[\[data-te-datepicker-cell-current\]\]\:border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  }

  .group:not([data-te-datepicker-cell-disabled]):not([data-te-datepicker-cell-selected]):hover .dark\:group-\[\:not\(\[data-te-datepicker-cell-disabled\]\)\:not\(\[data-te-datepicker-cell-selected\]\)\:hover\]\:bg-white\/10 {
    background-color: rgb(255 255 255 / 0.1);
  }

  .group:not([data-te-datepicker-cell-selected])[data-te-datepicker-cell-focused] .dark\:group-\[\:not\(\[data-te-datepicker-cell-selected\]\)\[data-te-datepicker-cell-focused\]\]\:bg-white\/10 {
    background-color: rgb(255 255 255 / 0.1);
  }

  .group[data-te-datepicker-cell-disabled] .dark\:group-\[\[data-te-datepicker-cell-disabled\]\]\:text-neutral-500 {
    --tw-text-opacity: 1;
    color: rgb(115 115 115 / var(--tw-text-opacity, 1));
  }

  .peer:focus ~ .dark\:peer-focus\:text-gray-200 {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .peer:focus ~ .dark\:peer-focus\:text-primary {
    --tw-text-opacity: 1;
    color: rgb(79 70 229 / var(--tw-text-opacity, 1));
  }

  .dark\:data-\[te-autocomplete-item-active\]\:bg-white\/30[data-te-autocomplete-item-active] {
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-buttons-timepicker\]\:bg-zinc-700[data-te-buttons-timepicker] {
    --tw-bg-opacity: 1;
    background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
  }

  .dark\:data-\[te-input-disabled\]\:bg-zinc-600[data-te-input-disabled] {
    --tw-bg-opacity: 1;
    background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
  }

  .dark\:data-\[te-input-multiple-active\]\:bg-white\/30[data-te-input-multiple-active] {
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-input-state-active\]\:bg-white\/30[data-te-input-state-active] {
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-select-option-selected\]\:data-\[te-input-state-active\]\:bg-white\/30[data-te-input-state-active][data-te-select-option-selected] {
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-select-option-disabled\]\:text-gray-400[data-te-select-option-disabled] {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }
}

@media (min-width: 768px) {
  @media (prefers-color-scheme: dark) {
    .md\:dark\:border-transparent {
      border-color: transparent;
    }
  }
}

.\[\&\.ps--clicking\]\:\!bg-\[\#eee\].ps--clicking {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1)) !important;
}

.\[\&\.ps--clicking\]\:\!opacity-90.ps--clicking {
  opacity: 0.9 !important;
}

@media (prefers-color-scheme: dark) {
  .dark\:\[\&\.ps--clicking\]\:\!bg-\[\#555\].ps--clicking {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(85 85 85 / var(--tw-bg-opacity, 1)) !important;
  }
}

.\[\&\:\:-webkit-scrollbar-button\]\:block::-webkit-scrollbar-button {
  display: block;
}

.\[\&\:\:-webkit-scrollbar-button\]\:h-0::-webkit-scrollbar-button {
  height: 0px;
}

.\[\&\:\:-webkit-scrollbar-button\]\:bg-transparent::-webkit-scrollbar-button {
  background-color: transparent;
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:h-\[50px\]::-webkit-scrollbar-thumb {
  height: 50px;
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:rounded::-webkit-scrollbar-thumb {
  border-radius: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:bg-\[\#999\]::-webkit-scrollbar-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.\[\&\:\:-webkit-scrollbar-track-piece\]\:rounded-none::-webkit-scrollbar-track-piece {
  border-radius: 0px;
}

.\[\&\:\:-webkit-scrollbar-track-piece\]\:rounded-l::-webkit-scrollbar-track-piece {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar-track-piece\]\:bg-transparent::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

.\[\&\:\:-webkit-scrollbar\]\:h-1::-webkit-scrollbar {
  height: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar\]\:w-1::-webkit-scrollbar {
  width: 0.25rem;
}

.hover\:\[\&\:not\(\[data-te-autocomplete-option-disabled\]\)\]\:bg-black\/5:not([data-te-autocomplete-option-disabled]):hover {
  background-color: rgb(0 0 0 / 0.05);
}

@media (prefers-color-scheme: dark) {
  .dark\:hover\:\[\&\:not\(\[data-te-autocomplete-option-disabled\]\)\]\:bg-white\/30:not([data-te-autocomplete-option-disabled]):hover {
    background-color: rgb(255 255 255 / 0.3);
  }
}

.\[\&\:not\(\[data-te-input-placeholder-active\]\)\]\:placeholder\:opacity-0:not([data-te-input-placeholder-active])::-moz-placeholder {
  opacity: 0;
}

.\[\&\:not\(\[data-te-input-placeholder-active\]\)\]\:placeholder\:opacity-0:not([data-te-input-placeholder-active])::placeholder {
  opacity: 0;
}

.hover\:\[\&\:not\(\[data-te-select-option-disabled\]\)\]\:bg-black\/5:not([data-te-select-option-disabled]):hover {
  background-color: rgb(0 0 0 / 0.05);
}

@media (prefers-color-scheme: dark) {
  .dark\:hover\:\[\&\:not\(\[data-te-select-option-disabled\]\)\]\:bg-white\/30:not([data-te-select-option-disabled]):hover {
    background-color: rgb(255 255 255 / 0.3);
  }
}

.\[\&\:nth-child\(odd\)\]\:bg-neutral-50:nth-child(odd) {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

@media (prefers-color-scheme: dark) {
  .\[\&\:nth-child\(odd\)\]\:dark\:bg-neutral-700:nth-child(odd) {
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
  }
}

.\[\&\>svg\]\:pointer-events-none>svg {
  pointer-events: none;
}

.\[\&\>svg\]\:mx-auto>svg {
  margin-left: auto;
  margin-right: auto;
}

.\[\&\>svg\]\:h-4>svg {
  height: 1rem;
}

.\[\&\>svg\]\:h-5>svg {
  height: 1.25rem;
}

.\[\&\>svg\]\:h-6>svg {
  height: 1.5rem;
}

.\[\&\>svg\]\:w-4>svg {
  width: 1rem;
}

.\[\&\>svg\]\:w-5>svg {
  width: 1.25rem;
}

.\[\&\>svg\]\:w-6>svg {
  width: 1.5rem;
}

.\[\&\>svg\]\:rotate-180>svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:fill-neutral-500>svg {
  fill: #737373;
}

@media (prefers-color-scheme: dark) {
  .dark\:\[\&\>svg\]\:fill-white>svg {
    fill: #fff;
  }
}
