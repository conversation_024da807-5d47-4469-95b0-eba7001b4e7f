import { lazy } from "react";

export const AddAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AddAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminLoginPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminSignUpPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

// User Pages
export const CreditHomePage = lazy(() => {
  const __import = import("@/pages/User/Credit/Home");
  __import.finally(() => {});
  return __import;
});

export const BankHomePage = lazy(() => {
  const __import = import("@/pages/User/Bank");
  __import.finally(() => {});
  return __import;
});

export const AffiliateOffersPage = lazy(() => {
  const __import = import("@/pages/User/Affiliate");
  __import.finally(() => {});
  return __import;
});

// OTHERS

export const TestComponents = lazy(() => {
  const __import = import("@/pages/PG/Custom/TestComponents");
  __import.finally(() => {});
  return __import;
});
