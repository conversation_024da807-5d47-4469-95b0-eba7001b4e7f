import React, { useCallback, useEffect, useState } from "react";
import { Routes, Route } from "react-router-dom";
// import { AuthContext } from "@/context/Auth";
// import { GlobalContext } from "@/context/Global";

import PrivateRoute from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import { PublicWrapper } from "@/components/PublicWrapper";
import { NotFoundPage } from "@/pages/404";
import { SnackBar } from "@/components/SnackBar";
import { SessionExpiredModal } from "@/components/SessionExpiredModal";

// generatePagesRoutes
import { AdminWrapper } from "@/components/AdminWrapper";

import {
  AdminForgotPage,
  AdminLoginPage,
  AdminProfilePage,
  AdminResetPage,
  LandingPage,
  MagicLoginVerifyPage,
  UserMagicLoginPage,
  ListAdminWireframeTablePage,
  AdminSignUpPage,
  TestComponents,
  CreditHomePage,
  BankHomePage,
  AffiliateOffersPage,
} from "./LazyLoad";

import EditWireframePage from "@/pages/Admin/Edit/EditWireframePage";
import { useContexts } from "@/hooks/useContexts";
import { RoleEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { RouteChangeModal } from "@/components/RouteChangeModal";
import RewardsPage from "@/pages/User/Rewards/RewardsPage";
import BonusTrackerPage from "@/pages/User/Rewards/BonusTrackerPage";
import CardDetailPage from "@/pages/User/Rewards/CardDetailPage";
import AccountSettingsPage from "@/pages/User/Setting/AccountSettingsPage";
import NotificationsPage from "@/pages/User/NotificationsPage";
import ForgotPasswordPage from "@/pages/Auth/ForgotPasswordPage";
import ResetPasswordPage from "@/pages/Auth/ResetPasswordPage";
import LoginPage from "@/pages/Auth/LoginPage";
import SignUpPage from "@/pages/Auth/SignupPage";

export interface DynamicWrapperProps {
  isAuthenticated?: boolean;
  role?: RoleEnum;
  children: React.ReactNode;
}

export const DynamicWrapper: React.FC<DynamicWrapperProps> = ({
  isAuthenticated,
  role,
  children,
}) => {
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    if (role && [RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(role)) {
      return <AdminWrapper>{children}</AdminWrapper>;
    }
  }
};

export interface NotFoundProps {
  isAuthenticated?: boolean;
  role?: RoleEnum | null;
}

export const NotFound: React.FC<NotFoundProps> = ({
  isAuthenticated,
  role,
}) => {
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  if (isAuthenticated) {
    if (role && [RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(role)) {
      return (
        <AdminWrapper>
          <NotFoundPage />
        </AdminWrapper>
      );
    }
  }
};

export default () => {
  const {
    globalState,
    globalDispatch: dispatch,
    authState: state,
    setGlobalState,
  } = useContexts();

  const isOpen = globalState?.isOpen ?? false;
  const openRouteChangeModal = globalState?.openRouteChangeModal ?? false;

  const [screenSize, setScreenSize] = useState(window.innerWidth);

  // function setDimension(e: Event) {
  //   const target = e.currentTarget as Window;
  //   if (target.innerWidth >= 1024) {
  //     toggleSideBar(true);
  //   } else toggleSideBar(false);
  //   setScreenSize(target.innerWidth);
  // }

  // const toTop = () => {
  //   containerRef.current.scrollTo(0, 0);
  // };

  const portalChange = useCallback(
    (e: any) => {
      if (
        (e.ctrlKey || e.metaKey) &&
        e.shiftKey &&
        e.altKey &&
        ["r", "R"].includes(e.key)
      ) {
        setGlobalState("openRouteChangeModal", true);
      }
      // console.log("PORTAL CHANGE  >>", openRouteChangeModal, e);
      if (["Escape", "escape", "ESCAPE", "Esc", "esc"].includes(e.key)) {
        setGlobalState("openRouteChangeModal", false);
      }
    },
    [setGlobalState]
  );

  const toggleSideBar = (open: boolean) => {
    if (isOpen && screenSize < 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    } else if (!isOpen && screenSize >= 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    window.addEventListener(
      "resize",
      (e) => {
        const target = e.currentTarget as Window;
        if (target.innerWidth >= 1024) {
          toggleSideBar(true);
        } else toggleSideBar(false);
        setScreenSize(target.innerWidth);
      },
      { signal }
    );

    return () => {
      controller.abort();
    };
  }, [screenSize]);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    window.addEventListener("keydown", portalChange, { signal });

    return () => {
      controller.abort();
    };
  }, []);

  return (
    <div
      onClick={() => {
        isOpen ? toggleSideBar(false) : null;
      }}
      className={`h-svh grid grid-cols-1 grid-rows-[auto_1fr] min-h-svh max-h-svh overflow-y-hidden overflow-x-hidden bg-white`}
    >
      <Routes>
        <Route
          path="/admin/profile"
          element={
            <PrivateRoute
              access={"admin"}
              path={"/admin/profile"}
              element={
                <AdminWrapper>
                  <AdminProfilePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/edit-wireframe/:id"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/edit-wireframe/:id"}
              element={
                <AdminWrapper>
                  <EditWireframePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/build"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/build"}
              element={
                <AdminWrapper>
                  <ListAdminWireframeTablePage />
                </AdminWrapper>
              }
            />
          }
        />

        {/* Credit Card Home Page Route */}
        <Route
          path="/user/credit"
          element={
            <PublicRoute path={"/user/credit"} element={<CreditHomePage />} />
          }
        />

        {/* Bank Accounts Page Route */}
        <Route
          path="/user/bank"
          element={
            <PublicRoute path={"/user/bank"} element={<BankHomePage />} />
          }
        />

        {/* Affiliate Offers Page Route */}
        <Route
          path="/user/affiliate"
          element={
            <PublicRoute
              path={"/user/affiliate"}
              element={<AffiliateOffersPage />}
            />
          }
        />

        {/* User Rewards Page Route */}
        <Route
          path="/user/rewards"
          element={
            <PublicRoute path={"/user/rewards"} element={<RewardsPage />} />
          }
        />

        {/* Bonus Tracker Page Route */}
        <Route
          path="/user/rewards/bonus-tracker"
          element={
            <PublicRoute
              path={"/user/rewards/bonus-tracker"}
              element={<BonusTrackerPage />}
            />
          }
        />

        {/* Card Detail Page Route */}
        <Route
          path="/user/rewards/cards/:cardId"
          element={
            <PublicRoute
              path={"/user/rewards/cards/:cardId"}
              element={<CardDetailPage />}
            />
          }
        />

        {/* Account Settings Page Route */}
        <Route
          path="/user/settings"
          element={
            <PublicRoute
              path={"/user/settings"}
              element={<AccountSettingsPage />}
            />
          }
        />

        {/* Notifications Page Route */}
        <Route
          path="/user/notifications"
          element={
            <PublicRoute
              path={"/user/notifications"}
              element={<NotificationsPage />}
            />
          }
        />

        {/* Forgot Password Page Route */}
        <Route
          path="/user/auth/forgot-password"
          element={
            <PublicRoute
              path={"/user/auth/forgot-password"}
              element={<ForgotPasswordPage />}
            />
          }
        />

        {/* Reset Password Page Route */}
        <Route
          path="/user/auth/reset-password"
          element={
            <PublicRoute
              path={"/user/auth/reset-password"}
              element={<ResetPasswordPage />}
            />
          }
        />

        {/* Login Page Route */}

        <Route
          path="/user/auth/login"
          element={
            <PublicRoute path={"/user/auth/login"} element={<LoginPage />} />
          }
        />

        {/* Sign Up Page Route */}
        <Route
          path="/user/auth/signup"
          element={
            <PublicRoute path={"/user/auth/signup"} element={<SignUpPage />} />
          }
        />

        <Route
          path="/"
          element={
            <PublicRoute
              path={"/"}
              element={
                // <PublicWrapper>
                <LandingPage />
                // </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/login"
          element={
            <PublicRoute
              path={"/admin/login"}
              element={
                <PublicWrapper>
                  <AdminLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/sign-up"
          element={
            <PublicRoute
              path={"/admin/sign-up"}
              element={
                <PublicWrapper>
                  <AdminSignUpPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/forgot"
          element={
            <PublicRoute
              path={"/admin/forgot"}
              element={
                <PublicWrapper>
                  <AdminForgotPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/reset"
          element={
            <PublicRoute
              path={"/admin/reset"}
              element={
                <PublicWrapper>
                  <AdminResetPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login"
          element={
            <PublicRoute
              path={"/magic-login"}
              element={
                <PublicWrapper>
                  <UserMagicLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login/verify"
          element={
            <PublicRoute
              path={"/magic-login/verify"}
              element={
                <PublicWrapper>
                  <MagicLoginVerifyPage />
                </PublicWrapper>
              }
            />
          }
        />

        {/* Custom Routes */}

        <Route
          path="/test-components"
          element={
            <PublicRoute
              path={"/test-components"}
              element={
                <PublicWrapper>
                  <TestComponents />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path={"*"}
          element={
            <PublicRoute
              path={"*"}
              element={
                <NotFound
                  isAuthenticated={state?.isAuthenticated}
                  role={state?.role as RoleEnum | null}
                />
              }
            />
          }
        />
      </Routes>
      <SessionExpiredModal />
      <SnackBar />

      <LazyLoad>
        <RouteChangeModal
          isOpen={openRouteChangeModal}
          onClose={() => setGlobalState("openRouteChangeModal", false)}
          options={[
            ...(state?.isAuthenticated
              ? [
                  {
                    name: `${state?.role} Login`,
                    route: `/${state?.role}/login`,
                  },
                  { name: "Test Components", route: "/test-components" },
                ]
              : [
                  { name: "Admin Login", route: "/admin/login" },
                  { name: "User Login", route: "/user/login" },
                  { name: "Test Components", route: "/test-components" },
                ]),
          ]}
          title="Change Route"
        />
      </LazyLoad>
    </div>
  );
};
