import React, { useState, useEffect } from "react";
import { UserLayout } from "../../../components/UserLayout";
import HomeWithoutCard from "../../../components/Credit/HomeWithoutCard";
import HomeWithCard from "../../../components/Credit/HomeWithCard";
import { creditCardApi } from "../../../services/api";

const CreditHomePage: React.FC = () => {
  const [hasCards, setHasCards] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user has cards on component mount
    const checkHasCards = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await creditCardApi.hasCards();

        if (response.success) {
          setHasCards(response.hasCards);
        } else {
          setError(response.message || "Failed to check card status");
        }
      } catch (err: any) {
        setError(
          err.response?.data?.message ||
            "An error occurred while checking if you have cards"
        );
        console.error("Error checking cards:", err);
      } finally {
        setIsLoading(false);
      }
    };

    checkHasCards();
  }, []);

  if (isLoading) {
    return (
      <UserLayout>
        <div className="flex justify-center items-center min-h-[50vh]">
          <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </UserLayout>
    );
  }

  if (error) {
    return (
      <UserLayout>
        <div className="max-w-5xl mx-auto">
          <div
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
            role="alert"
          >
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      </UserLayout>
    );
  }

  return (
    <UserLayout>
      {/* Demo toggle button - remove in production */}
      {/* <div className="max-w-5xl mx-auto mb-4">
        <button
          onClick={toggleCardView}
          className="px-3 py-1 bg-gray-200 rounded text-sm mb-2"
        >
          Toggle View (Demo Only): {hasCards ? "With Cards" : "Without Cards"}
        </button>
      </div> */}

      {hasCards ? <HomeWithCard /> : <HomeWithoutCard />}
    </UserLayout>
  );
};

export default CreditHomePage;
