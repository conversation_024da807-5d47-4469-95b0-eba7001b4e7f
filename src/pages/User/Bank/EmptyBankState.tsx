import React from "react";
import { FaUniversity } from "react-icons/fa";

interface EmptyBankStateProps {
  onAddBankAccount: () => void;
}

const EmptyBankState: React.FC<EmptyBankStateProps> = ({
  onAddBankAccount,
}) => {
  return (
    <div className="bg-white rounded-md p-12 flex flex-col items-center justify-center text-center">
      <div className="bg-gray-100 p-6 rounded-full mb-5">
        <FaUniversity className="text-gray-400 text-4xl" />
      </div>
      <h3 className="text-xl font-medium text-gray-700 mb-2">
        You haven't added any bank accounts yet.
      </h3>
      <p className="text-gray-500 mb-6 max-w-lg">
        Add your bank accounts to track balances, link credit cards, and manage
        your financial overview.
      </p>
      <button
        onClick={onAddBankAccount}
        className="bg-[#16c66c] hover:bg-[#238e4e] text-white font-medium py-2 px-4 rounded-md"
      >
        + Add Bank Account
      </button>
    </div>
  );
};

export default EmptyBankState;
