import React, { useState, useEffect, useCallback } from "react";
import { UserLayout } from "@/components/UserLayout";
import { FaSortAmountDown, FaFilter } from "react-icons/fa";
import { MdMarkEmailRead } from "react-icons/md";
import { notificationsApi, NotificationItem } from "@/services/api";
import { getNotificationIcon } from "@/utils/notificationIcons";

// Mock data for fallback when API fails
// Format date display
const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return "Just now";
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60)
    return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24)
    return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7)
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  if (diffInDays < 30)
    return `${Math.floor(diffInDays / 7)} week${Math.floor(diffInDays / 7) > 1 ? "s" : ""} ago`;

  return date.toLocaleDateString();
};

// Helper to create notification display text with bold entities
const createNotificationText = (notification: NotificationItem) => {
  const message = notification.message;

  // This is a simple implementation that assumes parts to be bolded are wrapped in special markers
  // In a real implementation, you might have a more sophisticated way to determine what to bold
  // based on metadata or patterns in the message

  // For now, we'll just pass the message directly and handle formatting in the UI
  return <p>{message}</p>;
};

const NotificationsPage: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [sortBy, setSortBy] = useState("date");
  const [filterByType, setFilterByType] = useState("all");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [unreadCount, setUnreadCount] = useState(0);

  const fetchNotifications = useCallback(
    async (newPage = 1, append = false) => {
      setLoading(true);
      setError(null);

      try {
        // Convert UI filter type to API types
        const types = filterByType === "all" ? [] : [filterByType];

        const response = await notificationsApi.fetchNotifications({
          page: newPage,
          limit: 10,
          unreadOnly: showUnreadOnly,
          types,
          sortBy: sortBy as "date" | "type",
          sortOrder: "desc",
        });

        setNotifications((prev) =>
          append ? [...prev, ...response.notifications] : response.notifications
        );
        setHasMore(response.hasMore);
        setTotalCount(response.totalCount);
        setUnreadCount(response.unreadCount);
        setPage(newPage);
      } catch (err) {
        console.error("Error fetching notifications:", err);
        setError("Failed to load notifications. Please try again.");

        // Fall back to mock data if API fails
        if (newPage === 1) {
          setNotifications([]);
          setHasMore(false);
          setTotalCount(0);
          setUnreadCount(0);
        }
      } finally {
        setLoading(false);
      }
    },
    [showUnreadOnly, filterByType, sortBy]
  );

  useEffect(() => {
    fetchNotifications(1, false);
  }, [fetchNotifications]);

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      fetchNotifications(page + 1, true);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationsApi.markAsRead("all");
      // Refresh notifications
      fetchNotifications(1, false);
    } catch (err) {
      console.error("Error marking notifications as read:", err);
      setError("Failed to mark notifications as read. Please try again.");
    }
  };

  const handleMarkAsRead = async (id: string) => {
    try {
      await notificationsApi.markAsRead([id]);
      // Update local state to avoid refetching
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, unread: false } : n))
      );
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (err) {
      console.error(`Error marking notification ${id} as read:`, err);
    }
  };

  const filteredNotifications = showUnreadOnly
    ? notifications.filter((n) => n.unread)
    : notifications;

  return (
    <UserLayout>
      <div className="container mx-auto max-w-5xl">
        <header className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900">
            Notifications
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            View all your notifications about transactions, rewards, offers, and
            more.
          </p>
        </header>

        <div className="bg-white shadow rounded-lg">
          {/* Filters and Actions */}
          <div className="p-4 border-b border-gray-200 flex flex-wrap items-center justify-between gap-x-4 gap-y-2">
            <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
              {/* Sort By Dropdown */}
              <div className="relative inline-block text-left">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-2 pl-3 pr-8 rounded-md leading-tight focus:outline-none focus:bg-white focus:border-gray-400 text-sm hover:border-gray-400 cursor-pointer"
                >
                  <option value="date">Date</option>
                  <option value="type">Type</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <FaSortAmountDown className="h-3 w-3" />
                </div>
              </div>

              {/* Filter By Type Dropdown */}
              <div className="relative inline-block text-left">
                <select
                  value={filterByType}
                  onChange={(e) => setFilterByType(e.target.value)}
                  className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-2 pl-3 pr-8 rounded-md leading-tight focus:outline-none focus:bg-white focus:border-gray-400 text-sm hover:border-gray-400 cursor-pointer"
                >
                  <option value="all">All Types</option>
                  <option value="payment_due">Payments</option>
                  <option value="reward">Rewards</option>
                  <option value="offer">Offers</option>
                  <option value="security">Security</option>
                  <option value="card_linked">Card Management</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <FaFilter className="h-3 w-3" />
                </div>
              </div>

              <label className="flex items-center text-sm text-gray-700 cursor-pointer whitespace-nowrap">
                <input
                  type="checkbox"
                  checked={showUnreadOnly}
                  onChange={(e) => setShowUnreadOnly(e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded-sm focus:ring-blue-500 mr-1.5"
                />
                Show Unread Only
              </label>
            </div>
            <button
              onClick={handleMarkAllAsRead}
              className="flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium whitespace-nowrap"
            >
              <MdMarkEmailRead className="h-4 w-4 mr-1" /> Mark All as Read
            </button>
          </div>

          {/* Notifications List */}
          <div className="divide-y divide-gray-100">
            {loading && notifications.length === 0 ? (
              <div className="p-6 text-center">
                <div className="animate-spin inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full mb-2"></div>
                <p className="text-gray-500">Loading notifications...</p>
              </div>
            ) : error && notifications.length === 0 ? (
              <div className="p-6 text-center text-red-500">
                <p>{error}</p>
                <button
                  onClick={() => fetchNotifications(1, false)}
                  className="mt-2 px-4 py-2 text-sm font-medium text-white bg-[#16c66c] rounded-md hover:bg-[#238e4e]"
                >
                  Retry
                </button>
              </div>
            ) : filteredNotifications.length > 0 ? (
              filteredNotifications.map((item) => {
                const iconConfig = getNotificationIcon(item.type);
                return (
                  <div
                    key={item.id}
                    className={`p-4 flex items-start hover:bg-gray-50 ${item.unread ? "bg-blue-50" : ""}`}
                    onClick={() => item.unread && handleMarkAsRead(item.id)}
                  >
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4 ${iconConfig.bgColor}`}
                    >
                      {React.cloneElement(
                        iconConfig.icon as React.ReactElement,
                        {
                          className: `h-5 w-5 ${iconConfig.iconColor}`,
                        }
                      )}
                    </div>
                    <div className="flex-grow">
                      <div className="text-sm text-gray-700">
                        {createNotificationText(item)}
                      </div>
                    </div>
                    <div className="flex-shrink-0 text-xs text-gray-500 ml-4 text-right whitespace-nowrap">
                      {formatTimeAgo(item.time)}
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="p-6 text-center text-gray-500">
                No notifications to display.
              </p>
            )}
          </div>

          {/* Load More Button */}
          {filteredNotifications.length > 0 && hasMore && (
            <div className="p-4 border-t border-gray-200 text-center">
              <button
                onClick={handleLoadMore}
                disabled={loading}
                className={`px-6 py-2 text-sm font-medium text-blue-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                {loading ? "Loading..." : "Load More Notifications"}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Footer within page content scroll */}
      <footer className="mt-8 py-6 text-center text-xs text-gray-500 border-t border-gray-200">
        <p className="mb-1">
          &copy; {new Date().getFullYear()} Stackeasy. All rights reserved.
        </p>
        <div className="space-x-4">
          <a href="#" className="hover:underline">
            Privacy Policy
          </a>
          <a href="#" className="hover:underline">
            Terms of Service
          </a>
          <a href="#" className="hover:underline">
            Contact Support
          </a>
        </div>
      </footer>
    </UserLayout>
  );
};

export default NotificationsPage;
