import React, { useState } from "react";
import { UserLayout } from "@/components/UserLayout";
import { FaUserCog, FaCreditCard, FaShieldAlt, FaBell } from "react-icons/fa";
import AlertsSettingsTab from "./AlertsSettingsTab";
import BillingSettingsTab from "./BillingSettingsTab";
import SecuritySettingsTab from "./SecuritySettingsTab";
import GeneralSettingsTab from "./GeneralSettingsTab";

type SettingsTab = "general" | "billing" | "security" | "alerts";

const AccountSettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SettingsTab>("general"); // Set general as default

  const renderTabContent = () => {
    switch (activeTab) {
      case "general":
        return <GeneralSettingsTab />;
      case "billing":
        return <BillingSettingsTab />;
      case "security":
        return <SecuritySettingsTab />;
      case "alerts":
        return <AlertsSettingsTab />;
      default:
        return <GeneralSettingsTab />;
    }
  };

  const TabButton: React.FC<{
    tabName: SettingsTab;
    currentTab: SettingsTab;
    onClick: (tab: SettingsTab) => void;
    icon: React.ReactNode;
    label: string;
  }> = ({ tabName, currentTab, onClick, icon, label }) => (
    <button
      onClick={() => onClick(tabName)}
      className={`flex items-center px-4 py-3 text-sm font-medium border-b-2 hover:text-blue-600 focus:outline-none
        ${
          currentTab === tabName
            ? "border-blue-600 text-blue-600"
            : "border-transparent text-gray-500 hover:border-gray-300"
        }
      `}
    >
      {icon}
      <span className="ml-2">{label}</span>
    </button>
  );

  return (
    <UserLayout>
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900">
            Account Settings
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your personal information, billing preferences, security
            settings, and alerts
          </p>
        </div>

        <div className="">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-4" aria-label="Tabs">
              <TabButton
                tabName="general"
                currentTab={activeTab}
                onClick={setActiveTab}
                icon={<FaUserCog className="h-5 w-5" />}
                label="General"
              />
              <TabButton
                tabName="billing"
                currentTab={activeTab}
                onClick={setActiveTab}
                icon={<FaCreditCard className="h-5 w-5" />}
                label="Billing"
              />
              <TabButton
                tabName="security"
                currentTab={activeTab}
                onClick={setActiveTab}
                icon={<FaShieldAlt className="h-5 w-5" />}
                label="Security"
              />
              <TabButton
                tabName="alerts"
                currentTab={activeTab}
                onClick={setActiveTab}
                icon={<FaBell className="h-5 w-5" />}
                label="Alerts"
              />
            </nav>
          </div>
          <div className="p-6">{renderTabContent()}</div>
        </div>
      </div>
    </UserLayout>
  );
};

export default AccountSettingsPage;
