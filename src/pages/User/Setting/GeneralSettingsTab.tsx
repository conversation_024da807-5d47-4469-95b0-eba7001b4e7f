import React, { useState, useEffect } from "react";
import { FaUserEdit, FaEnvelope, FaPhone, FaSave } from "react-icons/fa";
import { userApi } from "@/services/api";
import { toast } from "react-toastify";

interface InfoFieldProps {
  label: string;
  value: string;
  isEditing: boolean;
  onToggleEdit: () => void;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: string;
  isEmail?: boolean;
  isVerified?: boolean;
  onVerify?: () => void;
}

const InfoField: React.FC<InfoFieldProps> = ({
  label,
  value,
  isEditing,
  onToggleEdit,
  onChange,
  placeholder,
  type = "text",
  isEmail = false,
  isVerified = true, // Default to true, can be overridden
  onVerify,
}) => {
  return (
    <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-center">
      <dt className="text-sm font-medium text-gray-600">{label}</dt>
      <dd className="mt-1 flex text-sm text-gray-900 sm:mt-0 sm:col-span-2 sm:items-center">
        {isEditing ? (
          <input
            type={type}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder || `Enter your ${label.toLowerCase()}`}
            className="flex-grow p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        ) : (
          <span className="flex-grow py-2">{value}</span>
        )}
        {isEmail && !isVerified && !isEditing && (
          <span className="ml-2 mr-1 px-2 py-0.5 text-xs font-semibold bg-yellow-100 text-yellow-700 rounded-full">
            Unverified
          </span>
        )}
        {isEmail && !isVerified && !isEditing && onVerify && (
          <button
            type="button"
            onClick={onVerify}
            className="ml-2 px-3 py-1.5 text-xs font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md"
          >
            Verify Email
          </button>
        )}
        <button
          type="button"
          onClick={onToggleEdit}
          className="ml-3 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-100 hover:bg-blue-200 rounded-md"
        >
          {isEditing ? "Cancel" : "Edit"}
        </button>
      </dd>
    </div>
  );
};

const GeneralSettingsTab: React.FC = () => {
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");

  const [isEditingFullName, setIsEditingFullName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [isEditingPhoneNumber, setIsEditingPhoneNumber] = useState(false);

  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Fetch user profile data when component mounts
    const fetchUserProfile = async () => {
      setIsLoading(true);
      try {
        const response = await userApi.getProfile();
        if (response.success && response.user) {
          setFullName(response.user.fullName || "");
          setEmail(response.user.email || "");
          setPhoneNumber(response.user.phoneNumber || "");
          setIsEmailVerified(response.user.isEmailVerified || false);
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
        toast.error("Failed to load profile information");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, []);

  const handleSaveChanges = async () => {
    setIsSaving(true);
    try {
      const response = await userApi.updateProfile({ fullName });
      if (response.success) {
        toast.success("Profile updated successfully");
        setIsEditingFullName(false);
        setIsEditingEmail(false);
        setIsEditingPhoneNumber(false);
      } else {
        toast.error(response.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("An error occurred while updating your profile");
    } finally {
      setIsSaving(false);
    }
  };

  const handleVerifyEmail = () => {
    // This would typically send a verification email
    toast.info(`Verification email sent to ${email}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-48">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="bg-white p-6 sm:p-8 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          General Information
        </h3>

        <div className="space-y-6">
          {/* Full Name Field */}
          <div>
            <label
              htmlFor="fullName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Full Name
            </label>
            <div className="flex items-center">
              {isEditingFullName ? (
                <input
                  type="text"
                  id="fullName"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="flex-grow p-2.5 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
              ) : (
                <div className="flex-grow p-2.5 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                  {fullName || "Not set"}
                </div>
              )}
              <button
                type="button"
                onClick={() => setIsEditingFullName(!isEditingFullName)}
                className="ml-4 px-4 py-2 text-xs font-medium text-blue-600 bg-blue-100 hover:bg-blue-200 rounded-md whitespace-nowrap"
              >
                {isEditingFullName ? "Cancel" : "Edit"}
              </button>
            </div>
          </div>

          {/* Email Address Field */}
          <div>
            <label
              htmlFor="emailAddress"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email Address
            </label>
            <div className="flex items-center">
              {isEditingEmail ? (
                <input
                  type="email"
                  id="emailAddress"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-grow p-2.5 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
                  disabled={true} // Email editing disabled for now
                />
              ) : (
                <div className="flex-grow p-2.5 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                  {email || "Not set"}
                </div>
              )}
              {!isEditingEmail && !isEmailVerified && (
                <span className="ml-3 px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-700 rounded-full whitespace-nowrap">
                  Unverified
                </span>
              )}
              {!isEditingEmail && !isEmailVerified && (
                <button
                  type="button"
                  onClick={handleVerifyEmail}
                  className="ml-3 px-4 py-2 text-xs font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md whitespace-nowrap"
                >
                  Verify Email
                </button>
              )}
              <button
                type="button"
                onClick={() => setIsEditingEmail(!isEditingEmail)}
                className="ml-3 px-4 py-2 text-xs font-medium text-blue-600 bg-blue-100 hover:bg-blue-200 rounded-md whitespace-nowrap"
                disabled={true} // Email editing disabled for now
              >
                {isEditingEmail ? "Cancel" : "Edit"}
              </button>
            </div>
          </div>

          {/* Phone Number Field - removed as it's not in the backend yet */}
        </div>

        <div className="mt-8 pt-6 border-t border-gray-200 flex justify-end">
          <button
            type="button"
            onClick={handleSaveChanges}
            disabled={!isEditingFullName || isSaving}
            className="px-6 py-2.5 text-sm font-medium text-white bg-[#16c66c] border border-transparent rounded-md shadow-sm hover:bg-[#238e4e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <FaSave className="mr-2 h-4 w-4" /> Save Changes
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GeneralSettingsTab;
