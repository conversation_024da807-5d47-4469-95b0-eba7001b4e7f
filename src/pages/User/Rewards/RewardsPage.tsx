import React, { useState } from "react";
import { UserLayout } from "@/components/UserLayout";
import {
  FaSearch,
  FaChevronDown,
  FaFilter,
  FaChevronRight,
  FaExclamationTriangle,
  FaStar,
  FaGift,
} from "react-icons/fa";
import { Link } from "react-router-dom";
import OverviewTab from "./OverviewTab";
import MyRewardsTab from "./MyRewardsTab";
import BestCardByCategoryTab from "./BestCardByCategoryTab";
import MyOffersTab from "./MyOffersTab";
import PerksAndBenefitsTab from "./PerksAndBenefitsTab";
import AIRecommendationsTab from "./AIRecommendationsTab";

type Tab =
  | "overview"
  | "myRewards"
  | "bestCardByCategory"
  | "myOffers"
  | "perksAndBenefits"
  | "aiRecommendations";

const RewardsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<Tab>("overview");
  const [showAlert, setShowAlert] = useState(true);

  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return <OverviewTab />;
      case "myRewards":
        return <MyRewardsTab />;
      case "bestCardByCategory":
        return <BestCardByCategoryTab />;
      case "myOffers":
        return <MyOffersTab />;
      case "perksAndBenefits":
        return <PerksAndBenefitsTab />;
      case "aiRecommendations":
        return <AIRecommendationsTab />;
      default:
        return <OverviewTab />;
    }
  };

  return (
    <UserLayout>
      <div className="max-w-7xl mx-auto pb-6 px-4 sm:px-6 lg:px-8">
        {/* Header area */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
          <div>
            <h1 className="text-xl lg:text-2xl font-medium mb-1">
              Rewards & Benefits
            </h1>
            <p className="text-gray-600 text-sm">
              Track your rewards, explore exclusive offers, and manage your
              benefits with your credit cards.
            </p>
          </div>
        </div>

        {/* Search and filter */}
        <div className="flex flex-col sm:flex-row justify-between mb-4 space-y-3 sm:space-y-0">
          <div className="relative w-full sm:w-64">
            <input
              type="text"
              placeholder="Search rewards or benefits"
              className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
          <div className="relative w-full sm:w-auto">
            <select className="w-full sm:w-auto appearance-none bg-white border border-gray-300 rounded-md pl-3 pr-8 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>All Cards</option>
              <option>Chase Sapphire</option>
              <option>Amex Gold</option>
              <option>Citi Double Cash</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6 overflow-x-auto">
          <div className="flex space-x-4 sm:space-x-6 whitespace-nowrap">
            <button
              className={`py-3 px-1 sm:px-0 text-sm font-medium ${
                activeTab === "overview"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("overview")}
            >
              Overview
            </button>
            <button
              className={`py-3 px-1 sm:px-0 text-sm font-medium ${
                activeTab === "myRewards"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("myRewards")}
            >
              My Rewards
            </button>
            <button
              className={`py-3 px-1 sm:px-0 text-sm font-medium ${
                activeTab === "bestCardByCategory"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("bestCardByCategory")}
            >
              Best Card by Category
            </button>
            <button
              className={`py-3 px-1 sm:px-0 text-sm font-medium ${
                activeTab === "myOffers"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("myOffers")}
            >
              My Offers
            </button>
            <button
              className={`py-3 px-1 sm:px-0 text-sm font-medium ${
                activeTab === "perksAndBenefits"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("perksAndBenefits")}
            >
              Perks & Benefits
            </button>
            <button
              className={`py-3 px-1 sm:px-0 text-sm font-medium ${
                activeTab === "aiRecommendations"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("aiRecommendations")}
            >
              AI Recommendations
            </button>
          </div>
        </div>

        {/* Alert */}

        {/* Tab content */}
        {renderTabContent()}
      </div>
    </UserLayout>
  );
};

export default RewardsPage;
