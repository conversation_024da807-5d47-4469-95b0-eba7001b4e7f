import React, { useState, useEffect } from "react";
import {
  FaTrophy,
  FaClock,
  FaChartLine,
  FaArrowUp,
  FaCreditCard,
  FaBriefcase,
  FaExclamationTriangle,
  FaChartPie,
} from "react-icons/fa";
import { dataScientApi } from "../../../services/api";

const OverviewTab: React.FC = () => {
  const [overviewData, setOverviewData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch overview data
  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await dataScientApi.getOverview();

        if (response.success || response.total_rewards !== undefined) {
          setOverviewData(response);
        } else {
          // Use fallback data if API fails
          setOverviewData(getFallbackData());
        }
      } catch (err: any) {
        console.error("Failed to fetch overview data:", err);
        setError(err.message || "Failed to load overview data");
        // Use fallback data on error
        setOverviewData(getFallbackData());
      } finally {
        setIsLoading(false);
      }
    };

    fetchOverviewData();
  }, []);

  // Fallback data structure
  const getFallbackData = () => ({
    total_cards: 94,
    cards_by_issuer: {
      "Citi": 25,
      "Bank of America": 24,
      "Wells Fargo": 19,
      "U.S. Bank": 13,
      "American Express": 13
    },
    cards_by_type: {
      "Travel": 53,
      "General": 30,
      "Cashback": 7,
      "Business": 4
    },
    fee_distribution: {
      "free_cards": 90,
      "low_fee_cards": 2,
      "mid_fee_cards": 0,
      "high_fee_cards": 2
    },
    rewards_stats: {
      "cards_with_rewards": 42,
      "cards_with_signup_bonus": 6
    },
    last_updated: "2025-06-24T16:57:39.196543"
  });

  // Use API data or fallback data
  const data = overviewData || getFallbackData();

  // Transform issuer data for chart display
  const issuerData = Object.entries(data.cards_by_issuer || {}).map(([issuer, count]) => ({
    issuer: issuer.length > 10 ? issuer.substring(0, 10) + '...' : issuer,
    count: count as number
  }));

  // Calculate the max value for scaling the chart
  const maxIssuerCount = Math.max(...issuerData.map((d: any) => d.count));

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {/* Total Cards */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Total Cards</div>
          <div className="text-gray-500 text-xs mb-4">In our database</div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <FaCreditCard className="text-blue-500" />
          </div>

          <div className="text-3xl font-medium mb-1">
            {data.total_cards?.toLocaleString() || "94"}
          </div>

          <div className="flex items-center text-green-600 text-sm">
            <FaArrowUp className="mr-1" size={12} />
            {data.rewards_stats?.cards_with_rewards || 42} with rewards
          </div>

          <div className="mt-4 text-sm text-gray-600">
            Cards with signup bonus: {data.rewards_stats?.cards_with_signup_bonus || 6}
            <div className="mt-1 bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-[#16c66c] h-1.5 rounded-full"
                style={{ width: `${((data.rewards_stats?.cards_with_rewards || 42) / (data.total_cards || 94)) * 100}%` }}
              ></div>
            </div>
            <div className="mt-1 text-right text-xs text-gray-500">
              {Math.round(((data.rewards_stats?.cards_with_rewards || 42) / (data.total_cards || 94)) * 100)}% have rewards
            </div>
          </div>
        </div>

        {/* Fee Distribution */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Fee Distribution</div>
          <div className="text-gray-500 text-xs mb-4">Annual fee breakdown</div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <FaChartPie className="text-green-500" />
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Free Cards</span>
              </div>
              <span className="text-sm font-medium">{data.fee_distribution?.free_cards || 90}</span>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Low Fee</span>
              </div>
              <span className="text-sm font-medium">{data.fee_distribution?.low_fee_cards || 2}</span>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Mid Fee</span>
              </div>
              <span className="text-sm font-medium">{data.fee_distribution?.mid_fee_cards || 0}</span>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">High Fee</span>
              </div>
              <span className="text-sm font-medium">{data.fee_distribution?.high_fee_cards || 2}</span>
            </div>
          </div>

          <div className="mt-4 text-xs text-gray-500 text-center">
            {Math.round(((data.fee_distribution?.free_cards || 90) / (data.total_cards || 94)) * 100)}% are free cards
          </div>
        </div>

        {/* Cards by Issuer */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Cards by Issuer</div>
          <div className="text-gray-500 text-xs mb-4">
            Top card issuers
          </div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <FaChartLine className="text-purple-500" />
          </div>

          <div className="h-40 flex items-end space-x-2 sm:space-x-3 mt-4">
            {issuerData.slice(0, 5).map((issuer, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className={`w-full ${
                    index === 0 ? "bg-blue-600" :
                    index === 1 ? "bg-blue-500" :
                    index === 2 ? "bg-blue-400" :
                    "bg-blue-300"
                  } rounded-t-sm`}
                  style={{ height: `${(issuer.count / maxIssuerCount) * 100}%` }}
                ></div>
                <div className="mt-2 text-xs text-gray-500 text-center leading-tight">
                  {issuer.issuer}
                </div>
                <div className="text-xs font-medium text-gray-700">
                  {issuer.count}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;
