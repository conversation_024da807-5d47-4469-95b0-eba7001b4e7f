import React, { useState, useEffect } from "react";
import {
  FaR<PERSON>ot,
  FaChart<PERSON><PERSON>,
  FaExchangeAlt,
  FaExclamationTriangle,
  FaPlane,
  FaLock,
  FaMoneyBillWave,
  FaGasPump,
  FaCreditCard,
  FaShoppingCart,
} from "react-icons/fa";
import { aiRecommendationsApi, creditCardApi } from "../../../services/api";

interface Recommendation {
  id: string;
  type: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  iconBg: string;
  cards?: string[];
  action?: {
    text: string;
    variant: "default" | "warning";
  };
  match?: number;
}

interface CardRecommendation {
  id: string;
  name: string;
  type: string;
  icon: string;
  description: string;
  match: number;
}

const AIRecommendationsTab: React.FC = () => {
  const [insights, setInsights] = useState<Recommendation[]>([]);
  const [spendingInsights, setSpendingInsights] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch AI recommendations
  useEffect(() => {
    const fetchAIRecommendations = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, get user's credit cards
        const cardsResponse = await creditCardApi.getCards(1, 50);

        if (cardsResponse.success && cardsResponse.cards) {
          const cards = cardsResponse.cards;
          const cardNames = cards
            .filter((card: any) => !card.sponsored)
            .map((card: any) => card.name);

          // Build user context for AI recommendations
          const userContext = {
            spending_patterns: {
              dining: 800,
              travel: 600,
              groceries: 400,
              gas: 200,
              entertainment: 150,
              shopping: 300
            },
            preferences: cards.flatMap((card: any) => card.tags || []).filter(Boolean),
            current_cards: cardNames
          };

          const payload = {
            card_names: cardNames,
            user_context: userContext
          };

          try {
            const response = await aiRecommendationsApi.getPersonalizedRecommendations(payload);

            if (response.success || response.best_card_recommendations) {
              // Transform the API response to match our UI format
              const transformedRecommendations = transformApiResponse(response);
              setInsights(transformedRecommendations);

              // Store spending insights separately
              if (response.spending_insights) {
                setSpendingInsights(response.spending_insights);
              }
            } else {
              // Fall back to default recommendations
              setInsights(getDefaultInsights());
            }
          } catch (aiError) {
            console.warn("AI recommendations API failed, using defaults:", aiError);
            setInsights(getDefaultInsights());
          }
        } else {
          // No cards, use default insights
          setInsights(getDefaultInsights());
        }
      } catch (err: any) {
        console.error("Failed to fetch AI recommendations:", err);
        setError(err.message || "Failed to load AI recommendations");
        setInsights(getDefaultInsights());
      } finally {
        setIsLoading(false);
      }
    };

    fetchAIRecommendations();
  }, []);

  // Transform API response to UI format
  const transformApiResponse = (response: any): Recommendation[] => {
    const recommendations: Recommendation[] = [];

    // Add best card recommendations
    if (response.best_card_recommendations) {
      response.best_card_recommendations.forEach((rec: any, index: number) => {
        recommendations.push({
          id: `best_card_${index}`,
          type: `Best Card for ${rec.category}`,
          title: `${rec.reason}. ${rec.potential_rewards}`,
          description: "",
          icon: getIconForCategory(rec.category),
          iconBg: getIconBgForCategory(rec.category),
          cards: [rec.card_name],
          action: {
            text: `Set as default for ${rec.category.toLowerCase()}`,
            variant: "default"
          }
        });
      });
    }

    // Add APR alerts
    if (response.apr_alerts) {
      response.apr_alerts.forEach((alert: any, index: number) => {
        recommendations.push({
          id: `apr_alert_${index}`,
          type: "0% APR Ending Soon",
          title: `Your ${alert.card_name} 0% APR ends in ${alert.days_remaining} days. ${alert.recommendation}`,
          description: "",
          icon: <FaExclamationTriangle className="h-5 w-5 text-red-600" />,
          iconBg: "bg-red-100",
          cards: [alert.card_name],
          action: {
            text: "View options",
            variant: "warning"
          }
        });
      });
    }

    // Add balance transfer opportunities
    if (response.balance_transfer_opportunities) {
      response.balance_transfer_opportunities.forEach((transfer: any, index: number) => {
        recommendations.push({
          id: `balance_transfer_${index}`,
          type: "Balance Transfer Opportunity",
          title: `Transfer $${transfer.amount.toLocaleString()} from ${transfer.from_card} to ${transfer.to_card} to save $${transfer.potential_savings} in interest. ${transfer.reason}`,
          description: "",
          icon: <FaExchangeAlt className="h-5 w-5 text-green-600" />,
          iconBg: "bg-green-100",
          cards: [transfer.from_card, transfer.to_card],
          action: {
            text: "View transfer plan",
            variant: "default"
          }
        });
      });
    }

    return recommendations;
  };

  // Get icon for category
  const getIconForCategory = (category: string) => {
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('dining')) return <FaMoneyBillWave className="h-5 w-5 text-yellow-600" />;
    if (lowerCategory.includes('travel')) return <FaPlane className="h-5 w-5 text-blue-600" />;
    if (lowerCategory.includes('gas')) return <FaGasPump className="h-5 w-5 text-orange-600" />;
    if (lowerCategory.includes('groceries')) return <FaShoppingCart className="h-5 w-5 text-green-600" />;
    return <FaCreditCard className="h-5 w-5 text-gray-600" />;
  };

  // Get icon background for category
  const getIconBgForCategory = (category: string) => {
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('dining')) return "bg-yellow-100";
    if (lowerCategory.includes('travel')) return "bg-blue-100";
    if (lowerCategory.includes('gas')) return "bg-orange-100";
    if (lowerCategory.includes('groceries')) return "bg-green-100";
    return "bg-gray-100";
  };

  // Default insights fallback
  const getDefaultInsights = (): Recommendation[] => [
    {
      id: "1",
      type: "Spending Insight",
      title:
        "You're spending 30% of your income on dining. Use your Amex Gold for these purchases to earn 4x points, potentially adding 1,200 more points per month.",
      description: "",
      icon: <FaChartPie className="h-5 w-5 text-blue-600" />,
      iconBg: "bg-blue-100",
      cards: ["Amex Gold"],
      action: {
        text: "Set as default for dining",
        variant: "default",
      },
    },
    {
      id: "2",
      type: "Balance Transfer Opportunity",
      title:
        "Transfer $3,500 from Chase Sapphire to Brex Business Card with 0% APR for 12 months to save approximately $420 in interest.",
      description: "",
      icon: <FaExchangeAlt className="h-5 w-5 text-green-600" />,
      iconBg: "bg-green-100",
      cards: ["Chase Sapphire", "Brex Business Card"],
      action: {
        text: "View transfer plan",
        variant: "default",
      },
    },
    {
      id: "3",
      type: "Best Card for Travel: Chase Sapphire",
      title:
        "With your upcoming summer vacation, use Chase Sapphire to earn 3x points on travel and get complimentary travel insurance.",
      description: "",
      icon: <FaPlane className="h-5 w-5 text-blue-600" />,
      iconBg: "bg-blue-100",
      cards: ["Chase Sapphire"],
      action: {
        text: "Set as default for travel",
        variant: "default",
      },
    },
    {
      id: "4",
      type: "0% APR Ending Soon",
      title:
        "Your Chase Sapphire 0% APR ends in 30 days. To avoid paying 18.99% APR, consider a balance transfer or setting up a payment plan.",
      description: "",
      icon: <FaExclamationTriangle className="h-5 w-5 text-red-600" />,
      iconBg: "bg-red-100",
      cards: ["Chase Sapphire"],
      action: {
        text: "View options",
        variant: "warning",
      },
    },
  ];

  const cardRecommendations: CardRecommendation[] = [
    {
      id: "1",
      name: "Capital One Venture",
      type: "Travel Rewards Card",
      icon: "📊",
      description:
        "Based on your travel spending, this card would earn you approximately 25,000 more points annually compared to your current cards.",
      match: 94,
    },
    {
      id: "2",
      name: "Amazon Prime Rewards",
      type: "Cash Back Card",
      icon: "💲",
      description:
        "With your Amazon shopping habits, this card would earn you approximately $320 more in cashback annually.",
      match: 89,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <div className="flex items-center justify-center w-9 h-9 rounded-md bg-blue-100 mr-2">
          <FaRobot className="h-5 w-5 text-blue-600" />
        </div>
        <h2 className="text-lg font-medium text-gray-800">
          AI Recommendations
        </h2>
      </div>

      <div className="bg-blue-50 border border-blue-100 rounded-md p-4 mb-8">
        <h3 className="text-lg font-medium text-gray-800 mb-2">
          Smart Rewards Optimization
        </h3>
        <p className="text-gray-600 text-sm">
          Based on your spending patterns and card benefits, here are
          personalized recommendations to maximize your rewards.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {insights.map((insight) => (
          <div
            key={insight.id}
            className={`bg-white rounded-md border ${insight.type.includes("APR") ? "border-red-200" : "border-gray-200"} p-4`}
          >
            <div className="flex items-start mb-4">
              <div
                className={`${insight.iconBg} p-2 rounded-full mr-3 flex-shrink-0 mt-1`}
              >
                {insight.icon}
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">{insight.type}</div>
                <div className="text-gray-800">
                  {insight.title
                    .split(/\b(Amex Gold|Chase Sapphire|Brex Business Card)\b/)
                    .map((part, i) =>
                      [
                        "Amex Gold",
                        "Chase Sapphire",
                        "Brex Business Card",
                      ].includes(part) ? (
                        <span key={i} className="font-medium">
                          {part}
                        </span>
                      ) : (
                        part
                      )
                    )}
                </div>
              </div>
            </div>

            {insight.cards && insight.cards.length > 0 && (
              <div className="flex -space-x-2 mb-3">
                {insight.cards.map((card, idx) => (
                  <div
                    key={idx}
                    className={`w-8 h-8 rounded-full ${card.includes("Chase") ? "bg-blue-500" : card.includes("Amex") ? "bg-purple-500" : "bg-green-500"} flex items-center justify-center text-white text-xs border-2 border-white`}
                  >
                    {card.includes("Chase")
                      ? "CS"
                      : card.includes("Amex")
                        ? "AG"
                        : "BB"}
                  </div>
                ))}
              </div>
            )}

            {insight.action && (
              <button
                className={`text-sm ${
                  insight.action.variant === "warning"
                    ? "text-red-600 hover:text-red-700"
                    : "text-blue-600 hover:text-blue-700"
                } font-medium`}
              >
                {insight.action.text}
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Spending Insights Section */}
      {spendingInsights.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-800 mb-4">
            Spending Insights
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {spendingInsights.map((insight: any, index: number) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
                <div className="flex items-start mb-3">
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center mr-3 flex-shrink-0 ${
                    insight.category.toLowerCase() === 'dining' ? 'bg-yellow-100' :
                    insight.category.toLowerCase() === 'travel' ? 'bg-blue-100' :
                    insight.category.toLowerCase() === 'groceries' ? 'bg-green-100' :
                    insight.category.toLowerCase() === 'gas' ? 'bg-orange-100' :
                    'bg-purple-100'
                  }`}>
                    {insight.category.toLowerCase() === 'dining' ? (
                      <FaMoneyBillWave className="text-yellow-600 text-sm" />
                    ) : insight.category.toLowerCase() === 'travel' ? (
                      <FaPlane className="text-blue-600 text-sm" />
                    ) : insight.category.toLowerCase() === 'groceries' ? (
                      <FaShoppingCart className="text-green-600 text-sm" />
                    ) : insight.category.toLowerCase() === 'gas' ? (
                      <FaGasPump className="text-orange-600 text-sm" />
                    ) : (
                      <FaChartPie className="text-purple-600 text-sm" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm text-gray-800 mb-1">
                      {insight.category} Spending
                    </p>
                    <div className="mb-2">
                      <span className="bg-purple-100 text-purple-700 text-xs px-2 py-0.5 rounded-full">
                        {Math.round(insight.percentage * 100)}% of income
                      </span>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-gray-600 leading-relaxed mb-3">
                  {insight.insight}
                </p>
                <p className="text-xs text-blue-600 font-medium">
                  {insight.recommendation}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      <h3 className="text-lg font-medium text-gray-800 mb-4">
        New Card Recommendations
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {cardRecommendations.map((card) => (
          <div
            key={card.id}
            className="bg-white rounded-md border border-gray-200 p-4"
          >
            <div className="flex items-start mb-4">
              <div
                className={`w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center mr-3`}
              >
                <span className="text-lg">
                  {card.name.includes("Capital") ? "✈️" : "💰"}
                </span>
              </div>
              <div>
                <div className="font-medium">{card.name}</div>
                <div className="text-sm text-gray-500">{card.type}</div>
              </div>
            </div>

            <p className="text-sm text-gray-600 mb-4">{card.description}</p>

            <div className="flex justify-end">
              <div
                className={`px-2 py-1 rounded-md text-xs ${card.match >= 90 ? "bg-green-100 text-green-700" : "bg-blue-100 text-blue-700"}`}
              >
                {card.match}% Match
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AIRecommendationsTab;
