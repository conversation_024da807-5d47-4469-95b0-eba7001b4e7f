import { MkdInput } from "@/components/MkdInput";
import { LazyLoad } from "@/components/LazyLoad";
import { MkdButton } from "@/components/MkdButton";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useProjectHook } from "@/hooks/useProjectHook";

interface AddWireframePageProps {
  onClose: () => void;
  onSuccess: (e?: any) => void;
}

const AddWireframePage = ({ onClose, onSuccess }: AddWireframePageProps) => {
  const { handleSubmit, register, onSubmit, errors, isPending } =
    useProjectHook({
      onSuccess
    });

  return (
    <div className="h-full w-full">
      <form
        className="grid h-full max-h-full min-h-full w-full grid-rows-[1fr_auto] p-4 text-left"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex h-full max-h-full min-h-full flex-col gap-4 overflow-y-auto">
          <div>
            <LazyLoad>
              <MkdInput
                type="text"
                name="name"
                label="Name"
                register={register}
                errors={errors}
              />
            </LazyLoad>
          </div>
          <div>
            <LazyLoad>
              <MkdInput
                type="text"
                name="slug"
                label="Slug"
                register={register}
                errors={errors}
              />
            </LazyLoad>
          </div>
          <div>
            <LazyLoad>
              <MkdInput
                type="text"
                name="hostname"
                label="Hostname"
                disabled={true}
                errors={errors}
                register={register}
              />
            </LazyLoad>
          </div>
        </div>

        <div className="flex w-full items-center justify-between gap-5">
          <LazyLoad>
            <MkdButton
              showPlus={false}
              onClick={onClose}
              disabled={isPending}
              className="!w-1/2 !bg-transparent !text-black"
            >
              Cancel
            </MkdButton>
          </LazyLoad>
          <LazyLoad>
            <InteractiveButton
              type="submit"
              disabled={isPending}
              loading={isPending}
              className="!w-1/2"
            >
              Submit
            </InteractiveButton>
          </LazyLoad>
        </div>
      </form>
    </div>
  );
};

export default AddWireframePage;
