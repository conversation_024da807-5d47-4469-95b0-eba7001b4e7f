[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = http://23.29.118.76:3000/mkdlabs/baas_wireframe.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[branch "wireframe"]
	vscode-merge-base = master
[remote "origin2"]
	url = https://<EMAIL>/manaknightdev/baas_wireframe.git/
	fetch = +refs/heads/*:refs/remotes/origin2/*
[branch "revert"]
	vscode-merge-base = origin/master
[branch "pws"]
	vscode-merge-base = origin/master
[remote "git-origin"]
	url = https://github.com/mytechpassport/wireframetool.git
	fetch = +refs/heads/*:refs/remotes/git-origin/*
