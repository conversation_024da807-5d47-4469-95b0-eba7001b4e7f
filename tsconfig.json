{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noUnusedLocals": false, "strict": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["prettier"]}, "include": ["src", "src/types", "src/utils"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.e2e.ts", "dist", "server", "start-dev.js", "server.zip", "dev-dist", "playwright-report", "test-results", "src/test/e2e/global-setup.ts"], "references": [{"path": "./tsconfig.node.json"}]}